# BULAFITX Information

## Summary
BULAFITX is a mobile application designed to help users manage and track their fitness goals, workouts, and overall health. Built with React Native and Expo, it provides a seamless user experience for both Android and iOS devices, with role-based access for clients, trainers, nutritionists, physiotherapists, content creators, and administrators.

## Structure
- **app/**: Main application code using Expo Router for navigation
  - **(tabs)**: Main user interface tabs (home, workouts, meals, community, outdoor)
  - **(auth)**: Authentication screens (login, signup)
  - **(admin)**: Admin-specific screens
  - **(trainer)**: Trainer-specific screens
  - **(nutritionist)**: Nutritionist-specific screens
- **assets/**: Static assets including images
- **components/**: Reusable UI components
- **contexts/**: React context providers (AuthContext)
- **hooks/**: Custom React hooks
- **types/**: TypeScript type definitions

## Language & Runtime
**Language**: TypeScript/JavaScript
**Version**: TypeScript 5.8.3
**Framework**: React Native 0.79.5, React 19.0.0, Expo 53.0.17
**Package Manager**: npm/yarn

## Dependencies
**Main Dependencies**:
- expo-router: 5.1.3 (File-based routing)
- expo-font: 13.3.2 (Font management)
- react-native-reanimated: 3.17.4 (Animations)
- react-native-gesture-handler: 2.24.0 (Gesture handling)
- react-native-svg: 15.11.2 (SVG support)
- lucide-react-native: 0.475.0 (Icon library)

**Development Dependencies**:
- @babel/core: 7.25.2 (JavaScript compiler)
- @types/react: 19.0.10 (React type definitions)
- typescript: 5.8.3 (TypeScript compiler)

## Build & Installation
```bash
# Install dependencies
npm install
# or
yarn install

# Start development server
npm start
# or
yarn start

# Build for web
npm run build:web
# or
yarn build:web
```

## Application Architecture
The application uses Expo Router for file-based navigation with a role-based architecture:

1. **Authentication System**: Implemented through AuthContext with user roles (client, trainer, admin, nutritionist, physiotherapist, content creator)
2. **Role-Based Access**: Different screens and features based on user roles
3. **Tab-Based Navigation**: Main user interface organized in tabs
4. **Type Safety**: Comprehensive TypeScript definitions for user data and permissions

## Mobile Platform Support
**iOS**:
- Supports iPhone and iPad
- Bundle Identifier: com.bulafitx.app

**Android**:
- Adaptive icons
- Package: com.bulafitx.app

**Web**:
- Metro bundler
- Static output

## User Roles & Permissions
The application implements a comprehensive role-based permission system:
- **Client**: Access to workouts, progress tracking, nutrition plans
- **Trainer**: Client management, workout creation, session scheduling
- **Nutritionist**: Meal plan creation, nutrition consultations
- **Admin**: Platform management, user administration, analytics
- **Physiotherapist**: Rehabilitation programs, therapy sessions
- **Content Creator**: Content management, community engagement