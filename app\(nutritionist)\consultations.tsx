import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, StatusBar, Image } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Calendar, Clock, Plus, Video, MessageCircle, Phone, Star, Users } from 'lucide-react-native';
import Animated, { FadeInDown, FadeInRight } from 'react-native-reanimated';

export default function ConsultationsScreen() {
  const upcomingConsultations = [
    {
      id: '1',
      client: '<PERSON>',
      avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=100',
      time: '09:00 AM',
      date: 'Today',
      type: 'Initial Consultation',
      duration: '60 min',
      method: 'video',
      notes: 'Weight loss goals, dietary preferences assessment',
    },
    {
      id: '2',
      client: '<PERSON>',
      avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=100',
      time: '11:00 AM',
      date: 'Today',
      type: 'Follow-up',
      duration: '30 min',
      method: 'phone',
      notes: 'Review progress, adjust meal plan',
    },
    {
      id: '3',
      client: '<PERSON> <PERSON>',
      avatar: 'https://images.pexels.com/photos/1239288/pexels-photo-1239288.jpeg?auto=compress&cs=tinysrgb&w=100',
      time: '02:30 PM',
      date: 'Today',
      type: 'Meal Plan Review',
      duration: '45 min',
      method: 'video',
      notes: 'Discuss new dietary restrictions, recipe suggestions',
    },
  ];

  const consultationStats = [
    { label: 'This Week', value: '12', color: '#34C759' },
    { label: 'This Month', value: '48', color: '#007AFF' },
    { label: 'Avg Duration', value: '42min', color: '#FF9500' },
  ];

  const availableSlots = [
    { time: '10:00 AM', available: true },
    { time: '11:30 AM', available: false },
    { time: '01:00 PM', available: true },
    { time: '02:30 PM', available: false },
    { time: '04:00 PM', available: true },
    { time: '05:30 PM', available: true },
  ];

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#34C759" />
      
      <LinearGradient
        colors={['rgba(52, 199, 89, 0.95)', 'rgba(48, 209, 88, 0.95)']}
        style={styles.header}>
        <Text style={styles.headerTitle}>Consultations</Text>
        <Text style={styles.headerSubtitle}>Manage your client sessions</Text>
        
        <View style={styles.statsContainer}>
          {consultationStats.map((stat, index) => (
            <Animated.View
              key={stat.label}
              entering={FadeInDown.delay(index * 100)}
              style={styles.statCard}>
              <Text style={[styles.statValue, { color: stat.color }]}>{stat.value}</Text>
              <Text style={styles.statLabel}>{stat.label}</Text>
            </Animated.View>
          ))}
        </View>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.todaySection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Today's Schedule</Text>
            <TouchableOpacity style={styles.addButton}>
              <Plus size={20} color="#34C759" />
            </TouchableOpacity>
          </View>

          {upcomingConsultations.map((consultation, index) => (
            <Animated.View
              key={consultation.id}
              entering={FadeInRight.delay(200 + index * 100)}
              style={styles.consultationCard}>
              <View style={styles.timeSection}>
                <Text style={styles.timeText}>{consultation.time}</Text>
                <Text style={styles.durationText}>{consultation.duration}</Text>
              </View>

              <Image source={{ uri: consultation.avatar }} style={styles.clientAvatar} />

              <View style={styles.consultationInfo}>
                <Text style={styles.clientName}>{consultation.client}</Text>
                <Text style={styles.consultationType}>{consultation.type}</Text>
                <Text style={styles.consultationNotes}>{consultation.notes}</Text>
              </View>

              <View style={styles.consultationActions}>
                <View style={styles.methodIndicator}>
                  {consultation.method === 'video' ? (
                    <Video size={16} color="#007AFF" />
                  ) : (
                    <Phone size={16} color="#34C759" />
                  )}
                </View>
                <TouchableOpacity style={styles.startButton}>
                  <Text style={styles.startButtonText}>Start</Text>
                </TouchableOpacity>
              </View>
            </Animated.View>
          ))}
        </View>

        <View style={styles.availabilitySection}>
          <Text style={styles.sectionTitle}>Tomorrow's Availability</Text>
          <View style={styles.slotsGrid}>
            {availableSlots.map((slot, index) => (
              <Animated.View
                key={slot.time}
                entering={FadeInDown.delay(500 + index * 50)}
                style={[
                  styles.slotCard,
                  slot.available ? styles.availableSlot : styles.bookedSlot
                ]}>
                <TouchableOpacity style={styles.slotButton} disabled={!slot.available}>
                  <Text style={[
                    styles.slotTime,
                    slot.available ? styles.availableText : styles.bookedText
                  ]}>
                    {slot.time}
                  </Text>
                  <Text style={[
                    styles.slotStatus,
                    slot.available ? styles.availableText : styles.bookedText
                  ]}>
                    {slot.available ? 'Available' : 'Booked'}
                  </Text>
                </TouchableOpacity>
              </Animated.View>
            ))}
          </View>
        </View>

        <View style={styles.quickActionsSection}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.actionsGrid}>
            <TouchableOpacity style={styles.actionCard}>
              <LinearGradient
                colors={['#34C759', '#30D158']}
                style={styles.actionGradient}>
                <Calendar size={24} color="#FFFFFF" />
                <Text style={styles.actionText}>Schedule Session</Text>
              </LinearGradient>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.actionCard}>
              <LinearGradient
                colors={['#007AFF', '#5AC8FA']}
                style={styles.actionGradient}>
                <Users size={24} color="#FFFFFF" />
                <Text style={styles.actionText}>View All Clients</Text>
              </LinearGradient>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.actionCard}>
              <LinearGradient
                colors={['#FF9500', '#FFCC02']}
                style={styles.actionGradient}>
                <MessageCircle size={24} color="#FFFFFF" />
                <Text style={styles.actionText}>Send Message</Text>
              </LinearGradient>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.actionCard}>
              <LinearGradient
                colors={['#AF52DE', '#BF5AF2']}
                style={styles.actionGradient}>
                <Star size={24} color="#FFFFFF" />
                <Text style={styles.actionText}>Client Feedback</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    padding: 20,
    paddingTop: 60,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  headerTitle: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.9,
    marginBottom: 20,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 10,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.9,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  todaySection: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#000000',
  },
  addButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F2F2F7',
    justifyContent: 'center',
    alignItems: 'center',
  },
  consultationCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  timeSection: {
    marginRight: 16,
    alignItems: 'center',
    minWidth: 60,
  },
  timeText: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#000000',
  },
  durationText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
  },
  clientAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  consultationInfo: {
    flex: 1,
  },
  clientName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#000000',
    marginBottom: 2,
  },
  consultationType: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#34C759',
    marginBottom: 4,
  },
  consultationNotes: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
    lineHeight: 16,
  },
  consultationActions: {
    alignItems: 'center',
    gap: 8,
  },
  methodIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F2F2F7',
    justifyContent: 'center',
    alignItems: 'center',
  },
  startButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    backgroundColor: '#34C759',
  },
  startButtonText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  availabilitySection: {
    marginBottom: 24,
  },
  slotsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  slotCard: {
    width: '31%',
    borderRadius: 12,
    overflow: 'hidden',
  },
  availableSlot: {
    backgroundColor: '#FFFFFF',
    borderWidth: 2,
    borderColor: '#34C759',
  },
  bookedSlot: {
    backgroundColor: '#F2F2F7',
  },
  slotButton: {
    padding: 12,
    alignItems: 'center',
  },
  slotTime: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 2,
  },
  slotStatus: {
    fontSize: 10,
    fontFamily: 'Inter-Regular',
  },
  availableText: {
    color: '#34C759',
  },
  bookedText: {
    color: '#8E8E93',
  },
  quickActionsSection: {
    marginBottom: 40,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  actionCard: {
    width: '47%',
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  actionGradient: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 100,
  },
  actionText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
    marginTop: 8,
    textAlign: 'center',
  },
});