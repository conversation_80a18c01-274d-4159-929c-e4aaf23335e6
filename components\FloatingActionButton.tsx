import React, { useEffect } from 'react';
import { TouchableOpacity, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withRepeat,
  withSequence,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);
const AnimatedLinearGradient = Animated.createAnimatedComponent(LinearGradient);

interface FloatingActionButtonProps {
  onPress: () => void;
  children: React.ReactNode;
  colors?: string[];
  size?: number;
}

export default function FloatingActionButton({
  onPress,
  children,
  colors = ['#667eea', '#764ba2'],
  size = 60,
}: FloatingActionButtonProps) {
  const scale = useSharedValue(1);
  const rotate = useSharedValue(0);
  const shadowOpacity = useSharedValue(0.3);

  useEffect(() => {
    // Breathing animation
    scale.value = withRepeat(
      withSequence(
        withSpring(1.1, { damping: 8 }),
        withSpring(1, { damping: 8 })
      ),
      -1,
      true
    );

    // Subtle rotation
    rotate.value = withRepeat(
      withTiming(360, { duration: 20000 }),
      -1,
      false
    );

    // Shadow pulse
    shadowOpacity.value = withRepeat(
      withSequence(
        withTiming(0.6, { duration: 1500 }),
        withTiming(0.3, { duration: 1500 })
      ),
      -1,
      true
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: scale.value },
      { rotate: `${rotate.value}deg` },
    ],
    shadowOpacity: shadowOpacity.value,
  }));

  const handlePress = () => {
    scale.value = withSequence(
      withSpring(0.9, { damping: 10 }),
      withSpring(1.1, { damping: 8 })
    );
    onPress();
  };

  return (
    <AnimatedTouchableOpacity
      style={[
        styles.container,
        { width: size, height: size, borderRadius: size / 2 },
        animatedStyle,
      ]}
      onPress={handlePress}
      activeOpacity={0.8}>
      <AnimatedLinearGradient
        colors={colors}
        style={[styles.gradient, { borderRadius: size / 2 }]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}>
        {children}
      </AnimatedLinearGradient>
    </AnimatedTouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowRadius: 16,
    elevation: 8,
  },
  gradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});