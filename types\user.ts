export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  profileImage?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  
  // Role-specific data
  clientProfile?: ClientProfile;
  trainerProfile?: TrainerProfile;
  nutritionistProfile?: NutritionistProfile;
  physiotherapistProfile?: PhysiotherapistProfile;
  contentCreatorProfile?: ContentCreatorProfile;
}

export type UserRole = 
  | 'client' 
  | 'trainer' 
  | 'admin' 
  | 'nutritionist' 
  | 'physiotherapist' 
  | 'content_creator';

export interface ClientProfile {
  age: number;
  fitnessGoal: string;
  fitnessLevel: 'beginner' | 'intermediate' | 'advanced';
  height?: number;
  weight?: number;
  medicalConditions?: string[];
  assignedTrainer?: string;
  assignedNutritionist?: string;
  subscriptionPlan: 'free' | 'basic' | 'premium';
  membershipStartDate: string;
}

export interface TrainerProfile {
  specializations: string[];
  certifications: Certification[];
  experience: number; // years
  rating: number;
  totalClients: number;
  hourlyRate?: number;
  bio: string;
  availableSlots: TimeSlot[];
  isVerified: boolean;
}

export interface NutritionistProfile {
  specializations: string[];
  certifications: Certification[];
  experience: number;
  rating: number;
  totalClients: number;
  consultationRate?: number;
  bio: string;
  availableSlots: TimeSlot[];
  isVerified: boolean;
}

export interface PhysiotherapistProfile {
  specializations: string[];
  certifications: Certification[];
  experience: number;
  rating: number;
  totalClients: number;
  consultationRate?: number;
  bio: string;
  availableSlots: TimeSlot[];
  isVerified: boolean;
}

export interface ContentCreatorProfile {
  contentTypes: ('video' | 'blog' | 'podcast' | 'social')[];
  followers: number;
  totalContent: number;
  rating: number;
  bio: string;
  socialLinks: SocialLink[];
  isVerified: boolean;
}

export interface Certification {
  name: string;
  issuingOrganization: string;
  issueDate: string;
  expiryDate?: string;
  credentialId?: string;
}

export interface TimeSlot {
  dayOfWeek: number; // 0-6 (Sunday-Saturday)
  startTime: string; // HH:mm format
  endTime: string; // HH:mm format
  timezone: string;
}

export interface SocialLink {
  platform: string;
  url: string;
  followers?: number;
}

// Role permissions
export const ROLE_PERMISSIONS = {
  client: [
    'view_workouts',
    'track_progress',
    'book_sessions',
    'view_nutrition_plans',
    'join_challenges',
    'access_community'
  ],
  trainer: [
    'create_workouts',
    'manage_clients',
    'view_client_progress',
    'schedule_sessions',
    'create_programs',
    'access_trainer_tools'
  ],
  nutritionist: [
    'create_meal_plans',
    'manage_nutrition_clients',
    'view_client_nutrition_data',
    'schedule_consultations',
    'create_nutrition_content'
  ],
  physiotherapist: [
    'create_rehab_programs',
    'manage_rehab_clients',
    'view_client_health_data',
    'schedule_therapy_sessions',
    'create_recovery_content'
  ],
  content_creator: [
    'create_content',
    'manage_content',
    'view_content_analytics',
    'engage_with_community',
    'collaborate_with_trainers'
  ],
  admin: [
    'manage_all_users',
    'view_all_data',
    'manage_platform_settings',
    'handle_reports',
    'manage_payments',
    'access_analytics'
  ]
} as const;