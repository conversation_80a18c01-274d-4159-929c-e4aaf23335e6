import React, { useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, StatusBar, Image, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Users, Calendar, TrendingUp, DollarSign, Star, Clock, Target, Award, Bell, Plus, Heart, Utensils, Apple, Leaf } from 'lucide-react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withRepeat,
  FadeInDown,
  FadeInRight,
  FadeInUp,
} from 'react-native-reanimated';
import { useAuth } from '@/contexts/AuthContext';
import AnimatedProgressRing from '@/components/AnimatedProgressRing';

export default function NutritionistDashboard() {
  const { user, logout } = useAuth();
  const backgroundAnimation = useSharedValue(0);

  useEffect(() => {
    backgroundAnimation.value = withRepeat(
      withTiming(1, { duration: 8000 }),
      -1,
      true
    );
  }, []);

  const backgroundStyle = useAnimatedStyle(() => ({
    transform: [
      {
        translateY: backgroundAnimation.value * 20,
      },
    ],
    opacity: 0.1 + backgroundAnimation.value * 0.1,
  }));

  const handleLogout = async () => {
    try {
      console.log('Logout button pressed');
      await logout();
      console.log('Logout completed');
    } catch (error) {
      console.error('Logout error:', error);
      Alert.alert('Error', 'Failed to logout. Please try again.');
    }
  };

  const stats = [
    { label: 'Active Clients', value: '18', icon: Users, color: '#34C759', change: '+2' },
    { label: 'Consultations Today', value: '6', icon: Calendar, color: '#007AFF', change: '+1' },
    { label: 'Monthly Revenue', value: '$2,840', icon: DollarSign, color: '#FF9500', change: '+8%' },
    { label: 'Avg Rating', value: '4.9', icon: Star, color: '#FFD700', change: '+0.1' },
  ];

  const todaySchedule = [
    { time: '09:00', client: 'Maria Santos', type: 'Initial Consultation', duration: '60 min', avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=100' },
    { time: '11:00', client: 'James Wilson', type: 'Follow-up', duration: '30 min', avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=100' },
    { time: '14:30', client: 'Lisa Chen', type: 'Meal Plan Review', duration: '45 min', avatar: 'https://images.pexels.com/photos/1239288/pexels-photo-1239288.jpeg?auto=compress&cs=tinysrgb&w=100' },
    { time: '16:00', client: 'Robert Davis', type: 'Weight Management', duration: '45 min', avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=100' },
  ];

  const recentActivity = [
    { client: 'Maria Santos', action: 'Completed meal plan', time: '1h ago', type: 'plan', icon: Heart },
    { client: 'James Wilson', action: 'Logged food diary', time: '3h ago', type: 'log', icon: Utensils },
    { client: 'Lisa Chen', action: 'Booked consultation', time: '5h ago', type: 'booking', icon: Calendar },
    { client: 'Robert Davis', action: 'Achieved weight goal', time: '1d ago', type: 'achievement', icon: Target },
  ];

  const quickStats = [
    { label: 'Meal Plans Created', value: '24', color: '#34C759' },
    { label: 'Client Success Rate', value: '94%', color: '#007AFF' },
    { label: 'Avg Weight Loss', value: '8.5kg', color: '#FF9500' },
  ];

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#34C759" />
      
      {/* Animated Background */}
      <View style={styles.backgroundContainer}>
        <LinearGradient
          colors={['#667eea', '#764ba2', '#f093fb', '#f5576c']}
          style={styles.backgroundGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
        
        <Animated.View style={[styles.floatingElement, styles.element1, backgroundStyle]} />
        <Animated.View style={[styles.floatingElement, styles.element2, backgroundStyle]} />
        <Animated.View style={[styles.floatingElement, styles.element3, backgroundStyle]} />
        <Animated.View style={[styles.floatingElement, styles.element4, backgroundStyle]} />
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <LinearGradient
          colors={['rgba(52, 199, 89, 0.95)', 'rgba(48, 209, 88, 0.95)']}
          style={styles.header}>
          <View style={styles.headerContent}>
            <View>
              <Text style={styles.welcomeText}>Welcome back,</Text>
              <Text style={styles.nutritionistName}>{user?.firstName} {user?.lastName}</Text>
              <Text style={styles.roleText}>Certified Nutritionist</Text>
            </View>
            <View style={styles.headerActions}>
              <TouchableOpacity style={styles.notificationButton}>
                <Bell size={24} color="#FFFFFF" />
                <View style={styles.notificationBadge}>
                  <Text style={styles.notificationCount}>4</Text>
                </View>
              </TouchableOpacity>
              <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
                <Text style={styles.logoutText}>Logout</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Quick Stats */}
          <View style={styles.quickStatsContainer}>
            {quickStats.map((stat, index) => (
              <Animated.View
                key={stat.label}
                entering={FadeInDown.delay(index * 100)}
                style={styles.quickStatCard}>
                <Text style={[styles.quickStatValue, { color: stat.color }]}>{stat.value}</Text>
                <Text style={styles.quickStatLabel}>{stat.label}</Text>
              </Animated.View>
            ))}
          </View>
        </LinearGradient>

        {/* Stats Grid */}
        <View style={styles.statsSection}>
          <Text style={styles.sectionTitle}>Overview</Text>
          <View style={styles.statsGrid}>
            {stats.map((stat, index) => (
              <Animated.View
                key={stat.label}
                entering={FadeInDown.delay(200 + index * 100)}
                style={styles.statCard}>
                <View style={styles.statHeader}>
                  <View style={[styles.statIcon, { backgroundColor: stat.color }]}>
                    <stat.icon size={20} color="#FFFFFF" />
                  </View>
                  <Text style={[styles.statChange, { color: stat.color }]}>{stat.change}</Text>
                </View>
                <Text style={styles.statValue}>{stat.value}</Text>
                <Text style={styles.statLabel}>{stat.label}</Text>
              </Animated.View>
            ))}
          </View>
        </View>

        {/* Progress Section */}
        <View style={styles.progressSection}>
          <Text style={styles.sectionTitle}>Client Progress</Text>
          <View style={styles.progressGrid}>
            <Animated.View
              entering={FadeInRight.delay(400)}
              style={styles.progressCard}>
              <AnimatedProgressRing
                size={80}
                strokeWidth={6}
                progress={85}
                color="#34C759">
                <Apple size={24} color="#34C759" />
              </AnimatedProgressRing>
              <Text style={styles.progressLabel}>Nutrition Goals</Text>
              <Text style={styles.progressValue}>85%</Text>
            </Animated.View>
            
            <Animated.View
              entering={FadeInRight.delay(500)}
              style={styles.progressCard}>
              <AnimatedProgressRing
                size={80}
                strokeWidth={6}
                progress={92}
                color="#007AFF">
                <Target size={24} color="#007AFF" />
              </AnimatedProgressRing>
              <Text style={styles.progressLabel}>Weight Goals</Text>
              <Text style={styles.progressValue}>92%</Text>
            </Animated.View>
            
            <Animated.View
              entering={FadeInRight.delay(600)}
              style={styles.progressCard}>
              <AnimatedProgressRing
                size={80}
                strokeWidth={6}
                progress={78}
                color="#FF9500">
                <Leaf size={24} color="#FF9500" />
              </AnimatedProgressRing>
              <Text style={styles.progressLabel}>Adherence</Text>
              <Text style={styles.progressValue}>78%</Text>
            </Animated.View>
          </View>
        </View>

        {/* Today's Schedule */}
        <View style={styles.scheduleSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Today's Consultations</Text>
            <TouchableOpacity style={styles.addButton}>
              <Plus size={20} color="#34C759" />
            </TouchableOpacity>
          </View>
          
          {todaySchedule.map((session, index) => (
            <Animated.View
              key={index}
              entering={FadeInDown.delay(700 + index * 100)}
              style={styles.scheduleCard}>
              <View style={styles.scheduleTime}>
                <Text style={styles.timeText}>{session.time}</Text>
                <Text style={styles.durationText}>{session.duration}</Text>
              </View>
              <Image source={{ uri: session.avatar }} style={styles.clientAvatar} />
              <View style={styles.scheduleContent}>
                <Text style={styles.clientName}>{session.client}</Text>
                <Text style={styles.sessionType}>{session.type}</Text>
              </View>
              <TouchableOpacity style={styles.scheduleAction}>
                <Text style={styles.actionText}>Start</Text>
              </TouchableOpacity>
            </Animated.View>
          ))}
        </View>

        {/* Recent Activity */}
        <View style={styles.activitySection}>
          <Text style={styles.sectionTitle}>Recent Activity</Text>
          {recentActivity.map((activity, index) => (
            <Animated.View
              key={index}
              entering={FadeInUp.delay(1000 + index * 100)}
              style={styles.activityCard}>
              <View style={[
                styles.activityIcon,
                { backgroundColor: activity.type === 'plan' ? '#34C759' : 
                                  activity.type === 'log' ? '#007AFF' : 
                                  activity.type === 'booking' ? '#FF9500' : '#FFD700' }
              ]}>
                <activity.icon size={16} color="#FFFFFF" />
              </View>
              <View style={styles.activityContent}>
                <Text style={styles.activityClient}>{activity.client}</Text>
                <Text style={styles.activityAction}>{activity.action}</Text>
              </View>
              <Text style={styles.activityTime}>{activity.time}</Text>
            </Animated.View>
          ))}
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActionsSection}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActionsGrid}>
            <TouchableOpacity style={styles.quickActionCard}>
              <LinearGradient
                colors={['#34C759', '#30D158']}
                style={styles.quickActionGradient}>
                <Heart size={24} color="#FFFFFF" />
                <Text style={styles.quickActionText}>Create Meal Plan</Text>
              </LinearGradient>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.quickActionCard}>
              <LinearGradient
                colors={['#007AFF', '#5AC8FA']}
                style={styles.quickActionGradient}>
                <Calendar size={24} color="#FFFFFF" />
                <Text style={styles.quickActionText}>Schedule Consultation</Text>
              </LinearGradient>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.quickActionCard}>
              <LinearGradient
                colors={['#FF9500', '#FFCC02']}
                style={styles.quickActionGradient}>
                <Users size={24} color="#FFFFFF" />
                <Text style={styles.quickActionText}>View All Clients</Text>
              </LinearGradient>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.quickActionCard}>
              <LinearGradient
                colors={['#AF52DE', '#BF5AF2']}
                style={styles.quickActionGradient}>
                <TrendingUp size={24} color="#FFFFFF" />
                <Text style={styles.quickActionText}>Analytics</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
  },
  backgroundGradient: {
    flex: 1,
  },
  floatingElement: {
    position: 'absolute',
    borderRadius: 100,
    opacity: 0.1,
  },
  element1: {
    width: 200,
    height: 200,
    backgroundColor: '#FF6B35',
    top: '10%',
    left: '-10%',
  },
  element2: {
    width: 150,
    height: 150,
    backgroundColor: '#4ECDC4',
    top: '30%',
    right: '-5%',
  },
  element3: {
    width: 120,
    height: 120,
    backgroundColor: '#45B7D1',
    bottom: '20%',
    left: '10%',
  },
  element4: {
    width: 180,
    height: 180,
    backgroundColor: '#96CEB4',
    bottom: '5%',
    right: '-8%',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 60,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  welcomeText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.9,
  },
  nutritionistName: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  roleText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
    opacity: 0.8,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  notificationButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: 6,
    right: 6,
    width: 18,
    height: 18,
    borderRadius: 9,
    backgroundColor: '#FF3B30',
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationCount: {
    fontSize: 10,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  logoutButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  logoutText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  quickStatsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  quickStatCard: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
  },
  quickStatValue: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    marginBottom: 4,
  },
  quickStatLabel: {
    fontSize: 10,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.9,
    textAlign: 'center',
  },
  statsSection: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 22,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
    marginBottom: 16,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statCard: {
    width: '47%',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 16,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  statHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  statIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statChange: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
  },
  statValue: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#000000',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
  },
  progressSection: {
    padding: 20,
  },
  progressGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  progressCard: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  progressLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
    marginTop: 8,
    textAlign: 'center',
  },
  progressValue: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#000000',
    marginTop: 4,
  },
  scheduleSection: {
    padding: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  addButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scheduleCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  scheduleTime: {
    marginRight: 12,
    alignItems: 'center',
  },
  timeText: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#000000',
  },
  durationText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
  },
  clientAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  scheduleContent: {
    flex: 1,
  },
  clientName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#000000',
    marginBottom: 2,
  },
  sessionType: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
  },
  scheduleAction: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    backgroundColor: '#34C759',
  },
  actionText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  activitySection: {
    padding: 20,
  },
  activityCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  activityIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  activityContent: {
    flex: 1,
  },
  activityClient: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#000000',
    marginBottom: 2,
  },
  activityAction: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
  },
  activityTime: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
  },
  quickActionsSection: {
    padding: 20,
    paddingBottom: 40,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  quickActionCard: {
    width: '47%',
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  quickActionGradient: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 100,
  },
  quickActionText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
    marginTop: 8,
    textAlign: 'center',
  },
});