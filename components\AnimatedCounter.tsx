import React, { useEffect } from 'react';
import { Text, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  interpolate,
  runOnJS,
} from 'react-native-reanimated';

interface AnimatedCounterProps {
  value: number;
  duration?: number;
  style?: any;
  suffix?: string;
  prefix?: string;
}

export default function AnimatedCounter({
  value,
  duration = 2000,
  style,
  suffix = '',
  prefix = '',
}: AnimatedCounterProps) {
  const animatedValue = useSharedValue(0);
  const [displayValue, setDisplayValue] = React.useState(0);

  useEffect(() => {
    animatedValue.value = withTiming(value, { duration }, () => {
      runOnJS(setDisplayValue)(value);
    });
  }, [value, duration]);

  const animatedStyle = useAnimatedStyle(() => {
    const currentValue = interpolate(animatedValue.value, [0, value], [0, value]);
    runOnJS(setDisplayValue)(Math.floor(currentValue));
    
    return {
      transform: [
        {
          scale: interpolate(
            animatedValue.value,
            [0, value * 0.5, value],
            [0.8, 1.2, 1]
          ),
        },
      ],
    };
  });

  return (
    <Animated.Text style={[styles.text, style, animatedStyle]}>
      {prefix}{displayValue.toLocaleString()}{suffix}
    </Animated.Text>
  );
}

const styles = StyleSheet.create({
  text: {
    fontFamily: 'Inter-Bold',
  },
});