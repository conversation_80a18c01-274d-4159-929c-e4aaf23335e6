import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User, UserRole } from '@/types/user';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  signup: (userData: SignupData) => Promise<void>;
  logout: () => Promise<void>;
  updateUserRole: (role: UserRole) => Promise<void>;
  hasPermission: (permission: string) => boolean;
}

interface SignupData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  role: UserRole;
  age?: number;
  fitnessGoal?: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate checking for existing session
    const checkAuthState = async () => {
      try {
        // In a real app, you would check for stored tokens/session here
        // For now, we'll just set loading to false
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        console.error('Auth check failed:', error);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthState();
  }, []);

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock user data based on email for demo
      const mockUser: User = {
        id: '1',
        email,
        firstName: 'John',
        lastName: 'Doe',
        role: email.includes('trainer') ? 'trainer' : 
              email.includes('admin') ? 'admin' :
              email.includes('nutritionist') ? 'nutritionist' :
              email.includes('physio') ? 'physiotherapist' :
              email.includes('creator') ? 'content_creator' : 'client',
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // Add role-specific profile
      if (mockUser.role === 'client') {
        mockUser.clientProfile = {
          age: 28,
          fitnessGoal: 'weight_loss',
          fitnessLevel: 'beginner',
          subscriptionPlan: 'basic',
          membershipStartDate: new Date().toISOString(),
        };
      } else if (mockUser.role === 'trainer') {
        mockUser.trainerProfile = {
          specializations: ['Weight Loss', 'Strength Training'],
          certifications: [
            {
              name: 'Certified Personal Trainer',
              issuingOrganization: 'NASM',
              issueDate: '2020-01-15',
            }
          ],
          experience: 5,
          rating: 4.8,
          totalClients: 45,
          hourlyRate: 75,
          bio: 'Passionate fitness trainer with 5 years of experience helping clients achieve their goals.',
          availableSlots: [],
          isVerified: true,
        };
      } else if (mockUser.role === 'nutritionist') {
        mockUser.nutritionistProfile = {
          specializations: ['Weight Management', 'Sports Nutrition', 'Plant-Based Diets'],
          certifications: [
            {
              name: 'Registered Dietitian Nutritionist',
              issuingOrganization: 'CDR',
              issueDate: '2019-03-20',
            }
          ],
          experience: 6,
          rating: 4.9,
          totalClients: 32,
          consultationRate: 85,
          bio: 'Certified nutritionist specializing in sustainable lifestyle changes and evidence-based nutrition.',
          availableSlots: [],
          isVerified: true,
        };
      }

      setUser(mockUser);
    } catch (error) {
      console.error('Login failed:', error);
      throw new Error('Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const signup = async (userData: SignupData) => {
    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const newUser: User = {
        id: Date.now().toString(),
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        role: userData.role,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // Add role-specific profile
      if (userData.role === 'client') {
        newUser.clientProfile = {
          age: userData.age || 25,
          fitnessGoal: userData.fitnessGoal || 'general_fitness',
          fitnessLevel: 'beginner',
          subscriptionPlan: 'free',
          membershipStartDate: new Date().toISOString(),
        };
      } else if (userData.role === 'nutritionist') {
        newUser.nutritionistProfile = {
          specializations: [],
          certifications: [],
          experience: 0,
          rating: 0,
          totalClients: 0,
          consultationRate: 0,
          bio: '',
          availableSlots: [],
          isVerified: false,
        };
      }

      setUser(newUser);
    } catch (error) {
      console.error('Signup failed:', error);
      throw new Error('Signup failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);
      
      // Simulate API call to logout
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Clear user state
      setUser(null);
      
      // In a real app, you would also:
      // - Clear stored tokens/session data
      // - Clear any cached user data
      // - Reset any global state
      
      console.log('User logged out successfully');
    } catch (error) {
      console.error('Logout failed:', error);
      // Even if logout fails on server, clear local state
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const updateUserRole = async (role: UserRole) => {
    if (!user) return;
    
    setIsLoading(true);
    
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setUser({ ...user, role });
    } catch (error) {
      console.error('Role update failed:', error);
      throw new Error('Failed to update user role.');
    } finally {
      setIsLoading(false);
    }
  };

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    
    const { ROLE_PERMISSIONS } = require('@/types/user');
    const userPermissions = ROLE_PERMISSIONS[user.role] || [];
    return userPermissions.includes(permission);
  };

  return (
    <AuthContext.Provider value={{
      user,
      isLoading,
      login,
      signup,
      logout,
      updateUserRole,
      hasPermission,
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}