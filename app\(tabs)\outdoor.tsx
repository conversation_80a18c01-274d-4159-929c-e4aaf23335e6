import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, StatusBar } from 'react-native';
import { MapPin, Sun, Cloud, Wind, Droplets, Trophy, Leaf, Heart } from 'lucide-react-native';
import Animated, { 
  FadeInDown, 
  FadeInRight, 
  FadeInUp,
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withRepeat,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';

const weatherData = {
  temperature: 28,
  humidity: 75,
  uvIndex: 8,
  windSpeed: 12,
  recommendation: 'Perfect for outdoor activities! Don\'t forget sunscreen.',
};

const culturalWorkouts = [
  {
    title: 'Meke Dance Fitness',
    duration: '30 min',
    intensity: 'Medium',
    image: 'https://images.unsplash.com/photo-1578269174936-2709b6aeb913?w=500',
    description: 'Traditional Fijian dance movements for full-body workout',
  },
  {
    title: 'Coconut Tree Climbing',
    duration: '45 min',
    intensity: 'High',
    image: 'https://images.unsplash.com/photo-1596797038530-2c107229654b?w=500',
    description: 'Build strength and agility with traditional climbing techniques',
  },
  {
    title: 'Bilibili Racing',
    duration: '60 min',
    intensity: 'Medium',
    image: 'https://images.unsplash.com/photo-1596627116790-af6f46dddbae?w=500',
    description: 'Bamboo raft inspired cardio and balance training',
  },
];

const ecoActivities = [
  {
    title: 'Beach Cleanup Run',
    points: 150,
    participants: 24,
    image: 'https://images.unsplash.com/photo-1586962358070-16a0f05b8e67?w=500',
  },
  {
    title: 'Mangrove Planting',
    points: 200,
    participants: 18,
    image: 'https://images.unsplash.com/photo-1588668214407-6ea9a6d8c272?w=500',
  },
];

export default function OutdoorScreen() {
  const [selectedWorkout, setSelectedWorkout] = useState(null);
  const backgroundAnimation = useSharedValue(0);

  useEffect(() => {
    // Background animation
    backgroundAnimation.value = withRepeat(
      withTiming(1, { duration: 8000 }),
      -1,
      true
    );
  }, []);

  const backgroundStyle = useAnimatedStyle(() => ({
    transform: [
      {
        translateY: backgroundAnimation.value * 20,
      },
    ],
    opacity: 0.1 + backgroundAnimation.value * 0.1,
  }));

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#34C759" />
      
      {/* Animated Background */}
      <View style={styles.backgroundContainer}>
        <LinearGradient
          colors={['#667eea', '#764ba2', '#f093fb', '#f5576c']}
          style={styles.backgroundGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
        
        {/* Floating Background Elements */}
        <Animated.View style={[styles.floatingElement, styles.element1, backgroundStyle]} />
        <Animated.View style={[styles.floatingElement, styles.element2, backgroundStyle]} />
        <Animated.View style={[styles.floatingElement, styles.element3, backgroundStyle]} />
        <Animated.View style={[styles.floatingElement, styles.element4, backgroundStyle]} />
        
        {/* Geometric Patterns */}
        <View style={styles.geometricPattern}>
          {[...Array(20)].map((_, index) => (
            <Animated.View
              key={index}
              entering={FadeInUp.delay(index * 100).springify()}
              style={[
                styles.geometricShape,
                {
                  left: `${(index % 5) * 20 + 10}%`,
                  top: `${Math.floor(index / 5) * 25 + 10}%`,
                  opacity: 0.1,
                  transform: [
                    { rotate: `${index * 18}deg` },
                    { scale: 0.5 + (index % 3) * 0.3 },
                  ],
                },
              ]}
            />
          ))}
        </View>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <LinearGradient
          colors={['rgba(52, 199, 89, 0.95)', 'rgba(48, 209, 88, 0.95)']}
          style={styles.header}>
          <View style={styles.headerContent}>
            <View>
              <Text style={styles.headerTitle}>Outdoor Fitness</Text>
              <Text style={styles.headerSubtitle}>Powered by Fiji's Nature</Text>
            </View>
            <TouchableOpacity style={styles.locationButton}>
              <MapPin size={24} color="#FFFFFF" />
            </TouchableOpacity>
          </View>

          <Animated.View 
            entering={FadeInDown.delay(200)}
            style={styles.weatherCard}>
            <Text style={styles.weatherTitle}>Current Conditions</Text>
            <View style={styles.weatherGrid}>
              <View style={styles.weatherItem}>
                <Sun size={24} color="#FF9500" />
                <Text style={styles.weatherValue}>{weatherData.temperature}°C</Text>
              </View>
              <View style={styles.weatherItem}>
                <Droplets size={24} color="#007AFF" />
                <Text style={styles.weatherValue}>{weatherData.humidity}%</Text>
              </View>
              <View style={styles.weatherItem}>
                <Wind size={24} color="#32ADE6" />
                <Text style={styles.weatherValue}>{weatherData.windSpeed} km/h</Text>
              </View>
              <View style={styles.weatherItem}>
                <Sun size={24} color="#FF3B30" />
                <Text style={styles.weatherValue}>UV {weatherData.uvIndex}</Text>
              </View>
            </View>
            <Text style={styles.weatherRecommendation}>{weatherData.recommendation}</Text>
          </Animated.View>
        </LinearGradient>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Cultural Fitness</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {culturalWorkouts.map((workout, index) => (
              <Animated.View
                key={workout.title}
                entering={FadeInRight.delay(index * 100)}
                style={styles.workoutCard}>
                <Image source={{ uri: workout.image }} style={styles.workoutImage} />
                <View style={styles.workoutContent}>
                  <Text style={styles.workoutTitle}>{workout.title}</Text>
                  <Text style={styles.workoutDescription}>{workout.description}</Text>
                  <View style={styles.workoutDetails}>
                    <Text style={styles.workoutDuration}>{workout.duration}</Text>
                    <Text style={[styles.workoutIntensity, 
                      { backgroundColor: workout.intensity === 'High' ? '#FF3B30' : '#FF9500' }
                    ]}>{workout.intensity}</Text>
                  </View>
                </View>
              </Animated.View>
            ))}
          </ScrollView>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Eco-Fitness Challenges</Text>
          {ecoActivities.map((activity, index) => (
            <Animated.View
              key={activity.title}
              entering={FadeInDown.delay(300 + index * 100)}
              style={styles.ecoCard}>
              <Image source={{ uri: activity.image }} style={styles.ecoImage} />
              <View style={styles.ecoContent}>
                <Text style={styles.ecoTitle}>{activity.title}</Text>
                <View style={styles.ecoStats}>
                  <View style={styles.ecoStat}>
                    <Trophy size={16} color="#FF9500" />
                    <Text style={styles.ecoStatText}>{activity.points} points</Text>
                  </View>
                  <View style={styles.ecoStat}>
                    <Heart size={16} color="#FF3B30" />
                    <Text style={styles.ecoStatText}>{activity.participants} joined</Text>
                  </View>
                </View>
                <TouchableOpacity style={styles.ecoButton}>
                  <Leaf size={16} color="#FFFFFF" />
                  <Text style={styles.ecoButtonText}>Join Challenge</Text>
                </TouchableOpacity>
              </View>
            </Animated.View>
          ))}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
  },
  backgroundGradient: {
    flex: 1,
  },
  floatingElement: {
    position: 'absolute',
    borderRadius: 100,
    opacity: 0.1,
  },
  element1: {
    width: 200,
    height: 200,
    backgroundColor: '#FF6B35',
    top: '10%',
    left: '-10%',
  },
  element2: {
    width: 150,
    height: 150,
    backgroundColor: '#4ECDC4',
    top: '30%',
    right: '-5%',
  },
  element3: {
    width: 120,
    height: 120,
    backgroundColor: '#45B7D1',
    bottom: '20%',
    left: '10%',
  },
  element4: {
    width: 180,
    height: 180,
    backgroundColor: '#96CEB4',
    bottom: '5%',
    right: '-8%',
  },
  geometricPattern: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  geometricShape: {
    position: 'absolute',
    width: 20,
    height: 20,
    backgroundColor: '#FFFFFF',
    borderRadius: 4,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 60,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  headerTitle: {
    fontSize: 34,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  headerSubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.9,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  locationButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  weatherCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 16,
    padding: 20,
    marginTop: 10,
  },
  weatherTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#000000',
    marginBottom: 15,
  },
  weatherGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  weatherItem: {
    alignItems: 'center',
  },
  weatherValue: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#000000',
    marginTop: 8,
  },
  weatherRecommendation: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
    textAlign: 'center',
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 22,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
    marginBottom: 15,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  workoutCard: {
    width: 300,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 16,
    marginRight: 15,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  workoutImage: {
    width: '100%',
    height: 160,
  },
  workoutContent: {
    padding: 16,
  },
  workoutTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#000000',
    marginBottom: 8,
  },
  workoutDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
    marginBottom: 12,
  },
  workoutDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  workoutDuration: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
  },
  workoutIntensity: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  ecoCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 16,
    marginBottom: 15,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  ecoImage: {
    width: '100%',
    height: 200,
  },
  ecoContent: {
    padding: 16,
  },
  ecoTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#000000',
    marginBottom: 12,
  },
  ecoStats: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 16,
  },
  ecoStat: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  ecoStatText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
  },
  ecoButton: {
    backgroundColor: '#34C759',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  ecoButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
});