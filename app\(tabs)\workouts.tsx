import React, { useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, StatusBar } from 'react-native';
import { Search, Filter, Play, Clock, Dumbbell, Users, Zap, Target, Heart } from 'lucide-react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  withRepeat,
  withSequence,
  FadeInDown,
  FadeInRight,
  SlideInUp,
  FadeInUp,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import AnimatedProgressRing from '@/components/AnimatedProgressRing';
import FloatingActionButton from '@/components/FloatingActionButton';

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);
const AnimatedLinearGradient = Animated.createAnimatedComponent(LinearGradient);

const workoutCategories = [
  {
    title: 'Gym Workouts',
    description: 'Personalized routines with AI intensity scaling',
    image: 'https://images.pexels.com/photos/1552252/pexels-photo-1552252.jpeg?auto=compress&cs=tinysrgb&w=500',
    icon: Dumbbell,
    color: '#007AFF',
    workouts: 24,
  },
  {
    title: 'Yoga & Meditation',
    description: 'Fijian-themed guided sequences',
    image: 'https://images.pexels.com/photos/317157/pexels-photo-317157.jpeg?auto=compress&cs=tinysrgb&w=500',
    icon: Heart,
    color: '#FF3B30',
    workouts: 18,
  },
  {
    title: 'Sports Training',
    description: 'Rugby, Soccer, Netball, and more',
    image: 'https://images.pexels.com/photos/1628779/pexels-photo-1628779.jpeg?auto=compress&cs=tinysrgb&w=500',
    icon: Target,
    color: '#34C759',
    workouts: 32,
  },
];

const featuredWorkouts = [
  {
    title: 'Morning Flow Yoga',
    duration: '20 min',
    level: 'Beginner',
    calories: 180,
    image: 'https://images.pexels.com/photos/317157/pexels-photo-317157.jpeg?auto=compress&cs=tinysrgb&w=500',
    difficulty: 'Easy',
    rating: 4.8,
  },
  {
    title: 'Full Body HIIT',
    duration: '30 min',
    level: 'Intermediate',
    calories: 420,
    image: 'https://images.pexels.com/photos/416778/pexels-photo-416778.jpeg?auto=compress&cs=tinysrgb&w=500',
    difficulty: 'Hard',
    rating: 4.9,
  },
  {
    title: 'Rugby Power Drills',
    duration: '45 min',
    level: 'Advanced',
    calories: 580,
    image: 'https://images.pexels.com/photos/1628779/pexels-photo-1628779.jpeg?auto=compress&cs=tinysrgb&w=500',
    difficulty: 'Expert',
    rating: 4.7,
  },
];

export default function WorkoutsScreen() {
  const headerScale = useSharedValue(0.9);
  const searchScale = useSharedValue(0);
  const pulseAnimation = useSharedValue(1);
  useEffect(() => {
    headerScale.value = withSpring(1, { damping: 12 });
    searchScale.value = withSpring(1, { damping: 10 });
    
    pulseAnimation.value = withRepeat(
      withSequence(
        withTiming(1.05, { duration: 1500 }),
        withTiming(1, { duration: 1500 })
      ),
      -1,
      true
    );
  }, []);

  const headerStyle = useAnimatedStyle(() => ({
    transform: [{ scale: headerScale.value }],
  }));

  const searchStyle = useAnimatedStyle(() => ({
    transform: [{ scale: searchScale.value }],
  }));

  const pulseStyle = useAnimatedStyle(() => ({
    transform: [{ scale: pulseAnimation.value }],
  }));

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#667eea" />
      
      {/* Simple Background */}
      <View style={styles.backgroundContainer}>
        <LinearGradient
          colors={['#667eea', '#764ba2', '#f093fb', '#f5576c']}
          style={styles.backgroundGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Animated Header */}
        <Animated.View style={[styles.header, headerStyle]}>
          <LinearGradient
            colors={['rgba(102, 126, 234, 0.95)', 'rgba(118, 75, 162, 0.95)']}
            style={styles.headerGradient}>
            <Text style={styles.title}>Workouts</Text>
            <Text style={styles.subtitle}>Transform your body, elevate your spirit</Text>
            
            {/* Floating particles */}
            <View style={styles.particlesContainer}>
              {[...Array(8)].map((_, index) => (
                <Animated.View
                  key={index}
                  entering={FadeInDown.delay(index * 100).springify()}
                  style={[
                    styles.particle,
                    {
                      left: `${Math.random() * 90 + 5}%`,
                      top: `${Math.random() * 70 + 15}%`,
                    },
                  ]}
                />
              ))}
            </View>
          </LinearGradient>
        </Animated.View>

        {/* Search Section */}
        <Animated.View style={[styles.searchSection, searchStyle]}>
          <View style={styles.searchContainer}>
            <View style={styles.searchBar}>
              <Search size={20} color="#8E8E93" />
              <Text style={styles.searchPlaceholder}>Search workouts</Text>
            </View>
            <TouchableOpacity style={styles.filterButton}>
              <LinearGradient
                colors={['#667eea', '#764ba2']}
                style={styles.filterGradient}>
                <Filter size={20} color="#FFFFFF" />
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </Animated.View>

        {/* Workout Stats */}
        <Animated.View
          entering={SlideInUp.delay(300).springify()}
          style={[styles.statsSection, pulseStyle]}>
          <Text style={styles.sectionTitle}>Your Progress</Text>
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <AnimatedProgressRing
                size={60}
                strokeWidth={4}
                progress={75}
                color="#007AFF">
                <Dumbbell size={20} color="#007AFF" />
              </AnimatedProgressRing>
              <Text style={styles.statNumber}>12</Text>
              <Text style={styles.statLabel}>This Week</Text>
            </View>
            <View style={styles.statCard}>
              <AnimatedProgressRing
                size={60}
                strokeWidth={4}
                progress={60}
                color="#34C759">
                <Clock size={20} color="#34C759" />
              </AnimatedProgressRing>
              <Text style={styles.statNumber}>8.5h</Text>
              <Text style={styles.statLabel}>Total Time</Text>
            </View>
            <View style={styles.statCard}>
              <AnimatedProgressRing
                size={60}
                strokeWidth={4}
                progress={90}
                color="#FF9500">
                <Zap size={20} color="#FF9500" />
              </AnimatedProgressRing>
              <Text style={styles.statNumber}>2,840</Text>
              <Text style={styles.statLabel}>Calories</Text>
            </View>
          </View>
        </Animated.View>

        {/* Categories Section */}
        <View style={styles.categoriesSection}>
          <Text style={styles.sectionTitle}>Categories</Text>
          {workoutCategories.map((category, index) => (
            <Animated.View
              key={category.title}
              entering={FadeInDown.delay(index * 200).springify()}
              style={styles.categoryCard}>
              <AnimatedTouchableOpacity
                style={styles.categoryTouchable}
                onPress={() => console.log('Category pressed:', category.title)}>
                
                <Image source={{ uri: category.image }} style={styles.categoryImage} />
                
                <LinearGradient
                  colors={['transparent', 'rgba(0,0,0,0.8)']}
                  style={styles.categoryOverlay}>
                  
                  <View style={styles.categoryContent}>
                    <View style={[styles.categoryIcon, { backgroundColor: category.color }]}>
                      <category.icon size={24} color="#FFFFFF" />
                    </View>
                    <Text style={styles.categoryTitle}>{category.title}</Text>
                    <Text style={styles.categoryDescription}>{category.description}</Text>
                    <Text style={styles.categoryWorkouts}>{category.workouts} workouts</Text>
                  </View>
                  
                  <View style={styles.categoryAction}>
                    <Play size={20} color="#FFFFFF" fill="#FFFFFF" />
                  </View>
                </LinearGradient>
              </AnimatedTouchableOpacity>
            </Animated.View>
          ))}
        </View>

        {/* Featured Workouts */}
        <View style={styles.featuredSection}>
          <Text style={styles.sectionTitle}>Featured Workouts</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {featuredWorkouts.map((workout, index) => (
              <Animated.View
                key={workout.title}
                entering={FadeInRight.delay(index * 150).springify()}
                style={styles.workoutCard}>
                
                <TouchableOpacity
                  style={styles.workoutTouchable}
                  onPress={() => console.log('Workout pressed:', workout.title)}>
                  
                  <Image source={{ uri: workout.image }} style={styles.workoutImage} />
                  
                  <LinearGradient
                    colors={['transparent', 'rgba(0,0,0,0.9)']}
                    style={styles.workoutOverlay}>
                    
                    <View style={styles.workoutBadge}>
                      <Text style={styles.workoutLevel}>{workout.level}</Text>
                    </View>
                    
                    <View style={styles.workoutContent}>
                      <Text style={styles.workoutTitle}>{workout.title}</Text>
                      
                      <View style={styles.workoutDetails}>
                        <View style={styles.workoutDetail}>
                          <Clock size={14} color="#FFFFFF" />
                          <Text style={styles.workoutDetailText}>{workout.duration}</Text>
                        </View>
                        <View style={styles.workoutDetail}>
                          <Zap size={14} color="#FFFFFF" />
                          <Text style={styles.workoutDetailText}>{workout.calories} cal</Text>
                        </View>
                      </View>
                      
                      <View style={styles.workoutRating}>
                        <Text style={styles.ratingText}>★ {workout.rating}</Text>
                        <Text style={styles.difficultyText}>{workout.difficulty}</Text>
                      </View>
                    </View>
                    
                    <View style={styles.playButton}>
                      <Play size={16} color="#FFFFFF" fill="#FFFFFF" />
                    </View>
                  </LinearGradient>
                </TouchableOpacity>
              </Animated.View>
            ))}
          </ScrollView>
        </View>

        <View style={styles.fabContainer}>
          <FloatingActionButton
            onPress={() => console.log('Start workout pressed')}
            colors={['#667eea', '#764ba2']}>
            <Play size={28} color="#FFFFFF" fill="#FFFFFF" />
          </FloatingActionButton>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
  },
  backgroundGradient: {
    flex: 1,
  },
  header: {
    overflow: 'hidden',
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
  },
  headerGradient: {
    padding: 20,
    paddingTop: 60,
    paddingBottom: 40,
  },
  title: {
    fontSize: 36,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.9,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  particlesContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  particle: {
    position: 'absolute',
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
  },
  searchSection: {
    padding: 20,
    marginTop: -20,
    zIndex: 10,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  searchBar: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 16,
    padding: 16,
    gap: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
  },
  searchPlaceholder: {
    color: '#8E8E93',
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  filterButton: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 6,
  },
  filterGradient: {
    padding: 16,
  },
  statsSection: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 20,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 20,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 8,
  },
  statNumber: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#000000',
    marginTop: 12,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
  },
  categoriesSection: {
    padding: 20,
  },
  categoryCard: {
    marginBottom: 20,
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    elevation: 10,
  },
  categoryTouchable: {
    height: 180,
  },
  categoryImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  categoryOverlay: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-end',
    padding: 20,
  },
  categoryContent: {
    flex: 1,
  },
  categoryIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  categoryTitle: {
    fontSize: 22,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 6,
  },
  categoryDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.9,
    marginBottom: 6,
  },
  categoryWorkouts: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
    opacity: 0.8,
  },
  categoryAction: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  featuredSection: {
    paddingVertical: 20,
    paddingBottom: 100,
  },
  workoutCard: {
    width: 280,
    height: 220,
    marginLeft: 20,
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 20,
    elevation: 10,
  },
  workoutTouchable: {
    flex: 1,
  },
  workoutImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  workoutOverlay: {
    flex: 1,
    padding: 20,
  },
  workoutBadge: {
    alignSelf: 'flex-start',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginBottom: 'auto',
  },
  workoutLevel: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  workoutContent: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  workoutTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 12,
  },
  workoutDetails: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 8,
  },
  workoutDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  workoutDetailText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.9,
  },
  workoutRating: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#FFD700',
  },
  difficultyText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.8,
  },
  playButton: {
    position: 'absolute',
    top: 20,
    right: 20,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fabContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
});