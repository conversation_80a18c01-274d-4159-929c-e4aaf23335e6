import React, { useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, StatusBar, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Users, Calendar, TrendingUp, DollarSign, Star, Clock, Target, Award, Bell, Plus } from 'lucide-react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withRepeat,
  FadeInDown,
  FadeInRight,
  FadeInUp,
} from 'react-native-reanimated';
import { useAuth } from '@/contexts/AuthContext';
import AnimatedProgressRing from '@/components/AnimatedProgressRing';

export default function TrainerDashboard() {
  const { user, logout } = useAuth();
  const backgroundAnimation = useSharedValue(0);

  useEffect(() => {
    backgroundAnimation.value = withRepeat(
      withTiming(1, { duration: 8000 }),
      -1,
      true
    );
  }, []);

  const backgroundStyle = useAnimatedStyle(() => ({
    transform: [
      {
        translateY: backgroundAnimation.value * 20,
      },
    ],
    opacity: 0.1 + backgroundAnimation.value * 0.1,
  }));

  const handleLogout = async () => {
    try {
      console.log('Logout button pressed');
      await logout();
      console.log('Logout completed');
    } catch (error) {
      console.error('Logout error:', error);
      Alert.alert('Error', 'Failed to logout. Please try again.');
    }
  };

  const stats = [
    { label: 'Active Clients', value: '24', icon: Users, color: '#007AFF', change: '+3' },
    { label: 'Sessions Today', value: '8', icon: Calendar, color: '#34C759', change: '+2' },
    { label: 'Monthly Revenue', value: '$3,240', icon: DollarSign, color: '#FF9500', change: '+12%' },
    { label: 'Avg Rating', value: '4.8', icon: Star, color: '#FFD700', change: '+0.2' },
  ];

  const todaySchedule = [
    { time: '09:00', client: 'Sarah Johnson', type: 'Personal Training', duration: '60 min' },
    { time: '10:30', client: 'Mike Chen', type: 'Strength Training', duration: '45 min' },
    { time: '14:00', client: 'Emma Wilson', type: 'HIIT Session', duration: '30 min' },
    { time: '16:00', client: 'David Brown', type: 'Consultation', duration: '30 min' },
  ];

  const recentActivity = [
    { client: 'Sarah Johnson', action: 'Completed workout', time: '2h ago', type: 'workout' },
    { client: 'Mike Chen', action: 'Sent message', time: '4h ago', type: 'message' },
    { client: 'Emma Wilson', action: 'Booked session', time: '6h ago', type: 'booking' },
  ];

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#FF9500" />
      
      {/* Animated Background */}
      <View style={styles.backgroundContainer}>
        <LinearGradient
          colors={['#667eea', '#764ba2', '#f093fb', '#f5576c']}
          style={styles.backgroundGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
        
        <Animated.View style={[styles.floatingElement, styles.element1, backgroundStyle]} />
        <Animated.View style={[styles.floatingElement, styles.element2, backgroundStyle]} />
        <Animated.View style={[styles.floatingElement, styles.element3, backgroundStyle]} />
        <Animated.View style={[styles.floatingElement, styles.element4, backgroundStyle]} />
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <LinearGradient
          colors={['rgba(255, 149, 0, 0.95)', 'rgba(255, 159, 10, 0.95)']}
          style={styles.header}>
          <View style={styles.headerContent}>
            <View>
              <Text style={styles.welcomeText}>Welcome back,</Text>
              <Text style={styles.trainerName}>{user?.firstName} {user?.lastName}</Text>
              <Text style={styles.roleText}>Certified Trainer</Text>
            </View>
            <View style={styles.headerActions}>
              <TouchableOpacity style={styles.notificationButton}>
                <Bell size={24} color="#FFFFFF" />
                <View style={styles.notificationBadge}>
                  <Text style={styles.notificationCount}>3</Text>
                </View>
              </TouchableOpacity>
              <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
                <Text style={styles.logoutText}>Logout</Text>
              </TouchableOpacity>
            </View>
          </View>
        </LinearGradient>

        {/* Stats Grid */}
        <View style={styles.statsSection}>
          <Text style={styles.sectionTitle}>Overview</Text>
          <View style={styles.statsGrid}>
            {stats.map((stat, index) => (
              <Animated.View
                key={stat.label}
                entering={FadeInDown.delay(index * 100)}
                style={styles.statCard}>
                <View style={styles.statHeader}>
                  <View style={[styles.statIcon, { backgroundColor: stat.color }]}>
                    <stat.icon size={20} color="#FFFFFF" />
                  </View>
                  <Text style={[styles.statChange, { color: stat.color }]}>{stat.change}</Text>
                </View>
                <Text style={styles.statValue}>{stat.value}</Text>
                <Text style={styles.statLabel}>{stat.label}</Text>
              </Animated.View>
            ))}
          </View>
        </View>

        {/* Progress Section */}
        <View style={styles.progressSection}>
          <Text style={styles.sectionTitle}>Monthly Progress</Text>
          <View style={styles.progressGrid}>
            <Animated.View
              entering={FadeInRight.delay(200)}
              style={styles.progressCard}>
              <AnimatedProgressRing
                size={80}
                strokeWidth={6}
                progress={75}
                color="#007AFF">
                <Target size={24} color="#007AFF" />
              </AnimatedProgressRing>
              <Text style={styles.progressLabel}>Client Goals</Text>
              <Text style={styles.progressValue}>75%</Text>
            </Animated.View>
            
            <Animated.View
              entering={FadeInRight.delay(300)}
              style={styles.progressCard}>
              <AnimatedProgressRing
                size={80}
                strokeWidth={6}
                progress={90}
                color="#34C759">
                <Clock size={24} color="#34C759" />
              </AnimatedProgressRing>
              <Text style={styles.progressLabel}>Session Rate</Text>
              <Text style={styles.progressValue}>90%</Text>
            </Animated.View>
            
            <Animated.View
              entering={FadeInRight.delay(400)}
              style={styles.progressCard}>
              <AnimatedProgressRing
                size={80}
                strokeWidth={6}
                progress={85}
                color="#FF9500">
                <Award size={24} color="#FF9500" />
              </AnimatedProgressRing>
              <Text style={styles.progressLabel}>Satisfaction</Text>
              <Text style={styles.progressValue}>85%</Text>
            </Animated.View>
          </View>
        </View>

        {/* Today's Schedule */}
        <View style={styles.scheduleSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Today's Schedule</Text>
            <TouchableOpacity style={styles.addButton}>
              <Plus size={20} color="#FF9500" />
            </TouchableOpacity>
          </View>
          
          {todaySchedule.map((session, index) => (
            <Animated.View
              key={index}
              entering={FadeInDown.delay(500 + index * 100)}
              style={styles.scheduleCard}>
              <View style={styles.scheduleTime}>
                <Text style={styles.timeText}>{session.time}</Text>
                <Text style={styles.durationText}>{session.duration}</Text>
              </View>
              <View style={styles.scheduleContent}>
                <Text style={styles.clientName}>{session.client}</Text>
                <Text style={styles.sessionType}>{session.type}</Text>
              </View>
              <TouchableOpacity style={styles.scheduleAction}>
                <Text style={styles.actionText}>View</Text>
              </TouchableOpacity>
            </Animated.View>
          ))}
        </View>

        {/* Recent Activity */}
        <View style={styles.activitySection}>
          <Text style={styles.sectionTitle}>Recent Activity</Text>
          {recentActivity.map((activity, index) => (
            <Animated.View
              key={index}
              entering={FadeInUp.delay(700 + index * 100)}
              style={styles.activityCard}>
              <View style={[
                styles.activityIcon,
                { backgroundColor: activity.type === 'workout' ? '#34C759' : 
                                  activity.type === 'message' ? '#007AFF' : '#FF9500' }
              ]}>
                {activity.type === 'workout' && <TrendingUp size={16} color="#FFFFFF" />}
                {activity.type === 'message' && <Users size={16} color="#FFFFFF" />}
                {activity.type === 'booking' && <Calendar size={16} color="#FFFFFF" />}
              </View>
              <View style={styles.activityContent}>
                <Text style={styles.activityClient}>{activity.client}</Text>
                <Text style={styles.activityAction}>{activity.action}</Text>
              </View>
              <Text style={styles.activityTime}>{activity.time}</Text>
            </Animated.View>
          ))}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
  },
  backgroundGradient: {
    flex: 1,
  },
  floatingElement: {
    position: 'absolute',
    borderRadius: 100,
    opacity: 0.1,
  },
  element1: {
    width: 200,
    height: 200,
    backgroundColor: '#FF6B35',
    top: '10%',
    left: '-10%',
  },
  element2: {
    width: 150,
    height: 150,
    backgroundColor: '#4ECDC4',
    top: '30%',
    right: '-5%',
  },
  element3: {
    width: 120,
    height: 120,
    backgroundColor: '#45B7D1',
    bottom: '20%',
    left: '10%',
  },
  element4: {
    width: 180,
    height: 180,
    backgroundColor: '#96CEB4',
    bottom: '5%',
    right: '-8%',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 60,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  welcomeText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.9,
  },
  trainerName: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  roleText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
    opacity: 0.8,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  notificationButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: 6,
    right: 6,
    width: 18,
    height: 18,
    borderRadius: 9,
    backgroundColor: '#FF3B30',
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationCount: {
    fontSize: 10,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  logoutButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  logoutText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  statsSection: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 22,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
    marginBottom: 16,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statCard: {
    width: '47%',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 16,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  statHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  statIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statChange: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
  },
  statValue: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#000000',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
  },
  progressSection: {
    padding: 20,
  },
  progressGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  progressCard: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  progressLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
    marginTop: 8,
    textAlign: 'center',
  },
  progressValue: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#000000',
    marginTop: 4,
  },
  scheduleSection: {
    padding: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  addButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scheduleCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  scheduleTime: {
    marginRight: 16,
    alignItems: 'center',
  },
  timeText: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#000000',
  },
  durationText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
  },
  scheduleContent: {
    flex: 1,
  },
  clientName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#000000',
    marginBottom: 2,
  },
  sessionType: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
  },
  scheduleAction: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    backgroundColor: '#FF9500',
  },
  actionText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  activitySection: {
    padding: 20,
    paddingBottom: 40,
  },
  activityCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  activityIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  activityContent: {
    flex: 1,
  },
  activityClient: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#000000',
    marginBottom: 2,
  },
  activityAction: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
  },
  activityTime: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
  },
});