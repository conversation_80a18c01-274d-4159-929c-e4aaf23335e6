import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, StatusBar } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { ChevronRight, Activity, Target, Flame, Trophy, Plus, Star, Heart, Zap, Bell } from 'lucide-react-native';

export default function HomeScreen() {

  const progressData = [
    {
      number: 12,
      label: 'Workouts',
      color: '#007AFF',
      percentage: 75,
    },
    {
      number: 5200,
      label: 'Steps',
      color: '#34C759',
      percentage: 85,
    },
    {
      number: 320,
      label: 'Calories',
      color: '#FF9500',
      percentage: 60,
    },
  ];

  const featuredWorkouts = [
    {
      title: 'Morning Flow Yoga',
      duration: '20 min',
      intensity: 'Beginner',
      image: 'https://images.pexels.com/photos/317157/pexels-photo-317157.jpeg?auto=compress&cs=tinysrgb&w=500',
    },
    {
      title: 'HIIT Cardio Blast',
      duration: '30 min',
      intensity: 'Advanced',
      image: 'https://images.pexels.com/photos/416778/pexels-photo-416778.jpeg?auto=compress&cs=tinysrgb&w=500',
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#667eea" />
      
      {/* Simple Background */}
      <View style={styles.backgroundContainer}>
        <LinearGradient
          colors={['#667eea', '#764ba2', '#f093fb', '#f5576c']}
          style={styles.backgroundGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
      </View>
      
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <LinearGradient
          colors={['#667eea', '#764ba2']}
          style={styles.header}>
          
          <View style={styles.headerContent}>
            <View style={styles.headerLeft}>
              <Text style={styles.welcomeText}>Bula Vinaka! 🌺</Text>
              <Text style={styles.subText}>Ready to conquer your fitness goals?</Text>
            </View>
            <View style={styles.headerRight}>
              <TouchableOpacity style={styles.notificationButton}>
                <Bell size={24} color="#FFFFFF" />
                <View style={styles.notificationBadge}>
                  <Text style={styles.notificationCount}>3</Text>
                </View>
              </TouchableOpacity>
              <TouchableOpacity style={styles.profileButton}>
                <Image
                  source={{ uri: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=200' }}
                  style={styles.profileImage}
                />
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.quickStatsContainer}>
            <View style={styles.quickStat}>
              <Activity size={20} color="#FFFFFF" />
              <Text style={styles.quickStatValue}>2h 15m</Text>
              <Text style={styles.quickStatLabel}>Active</Text>
            </View>
            <View style={styles.quickStat}>
              <Flame size={20} color="#FFFFFF" />
              <Text style={styles.quickStatValue}>420</Text>
              <Text style={styles.quickStatLabel}>Calories</Text>
            </View>
            <View style={styles.quickStat}>
              <Target size={20} color="#FFFFFF" />
              <Text style={styles.quickStatValue}>8,240</Text>
              <Text style={styles.quickStatLabel}>Steps</Text>
            </View>
          </View>
        </LinearGradient>

        <View style={styles.challengeBanner}>
          <LinearGradient
            colors={['#FF6B35', '#F7931E']}
            style={styles.challengeGradient}>
            <View style={styles.challengeContent}>
              <Trophy size={28} color="#FFFFFF" />
              <View style={styles.challengeText}>
                <Text style={styles.challengeTitle}>Daily Challenge</Text>
                <Text style={styles.challengeSubtitle}>Complete 30 minutes of activity</Text>
                <View style={styles.challengeProgress}>
                  <View style={styles.progressBarBackground}>
                    <View style={styles.progressBarFill} />
                  </View>
                  <Text style={styles.progressText}>18/30 min</Text>
                </View>
              </View>
              <ChevronRight size={24} color="#FFFFFF" />
            </View>
          </LinearGradient>
        </View>

        <View style={styles.progressSection}>
          <View style={styles.progressHeader}>
            <View>
              <Text style={styles.sectionTitle}>Your Progress</Text>
              <Text style={styles.sectionSubtitle}>Keep pushing your limits!</Text>
            </View>
            <TouchableOpacity style={styles.viewAllButton}>
              <Text style={styles.viewAllText}>View All</Text>
              <ChevronRight size={20} color="#667eea" />
            </TouchableOpacity>
          </View>

          <View style={styles.progressGrid}>
            {progressData.map((item, index) => (
              <View key={item.label} style={styles.progressCard}>
                <View style={styles.progressRing}>
                  <View style={[styles.progressCircle, { borderColor: item.color }]}>
                    <Text style={[styles.progressNumber, { color: item.color }]}>
                      {item.number > 1000 ? `${(item.number / 1000).toFixed(1)}k` : item.number}
                    </Text>
                  </View>
                </View>
                <Text style={styles.progressLabel}>{item.label}</Text>
                <Text style={styles.progressPercentage}>{item.percentage}% of goal</Text>
              </View>
            ))}
          </View>
        </View>

        <View style={styles.featuredSection}>
          <View style={styles.featuredHeader}>
            <View>
              <Text style={styles.sectionTitle}>Featured Workouts</Text>
              <Text style={styles.sectionSubtitle}>Handpicked for you</Text>
            </View>
            <View style={styles.featuredBadge}>
              <Zap size={16} color="#FFD700" />
              <Text style={styles.featuredBadgeText}>Hot</Text>
            </View>
          </View>
          
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false} 
            style={styles.workoutScroll}>
            {featuredWorkouts.map((workout, index) => (
              <View key={workout.title} style={styles.workoutCard}>
                <Image source={{ uri: workout.image }} style={styles.workoutImage} />
                <LinearGradient
                  colors={['transparent', 'rgba(0,0,0,0.8)']}
                  style={styles.workoutOverlay}>
                  <View style={styles.workoutContent}>
                    <Text style={styles.workoutTitle}>{workout.title}</Text>
                    <View style={styles.workoutDetails}>
                      <Text style={styles.workoutDuration}>{workout.duration}</Text>
                      <Text style={styles.workoutIntensity}>{workout.intensity}</Text>
                    </View>
                  </View>
                </LinearGradient>
              </View>
            ))}
          </ScrollView>
        </View>

        <View style={styles.quickActions}>
          <Text style={styles.sectionTitle}>Quick Start</Text>
          <View style={styles.actionGrid}>
            {[
              { title: 'Start Workout', icon: Activity, color: '#007AFF' },
              { title: 'Track Run', icon: Target, color: '#34C759' },
              { title: 'Meal Plan', icon: Flame, color: '#FF9500' },
              { title: 'Join Challenge', icon: Trophy, color: '#FF3B30' },
            ].map((action, index) => (
              <TouchableOpacity key={action.title} style={styles.actionCard}>
                <View style={[styles.actionIcon, { backgroundColor: action.color }]}>
                  <action.icon size={28} color="#FFFFFF" />
                </View>
                <Text style={styles.actionTitle}>{action.title}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
  },
  backgroundGradient: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 30,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  headerLeft: {
    flex: 1,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  welcomeText: {
    fontSize: 32,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 6,
  },
  subText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.9,
  },
  notificationButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: 6,
    right: 6,
    width: 18,
    height: 18,
    borderRadius: 9,
    backgroundColor: '#FF3B30',
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationCount: {
    fontSize: 10,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  profileButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  profileImage: {
    width: '100%',
    height: '100%',
  },
  quickStatsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 20,
    padding: 16,
  },
  quickStat: {
    alignItems: 'center',
    flex: 1,
  },
  quickStatValue: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginTop: 6,
    marginBottom: 2,
  },
  quickStatLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.8,
  },
  challengeBanner: {
    margin: 20,
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: '#FF6B35',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  challengeGradient: {
    padding: 24,
  },
  challengeContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 20,
  },
  challengeText: {
    flex: 1,
  },
  challengeTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 6,
  },
  challengeSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.9,
    marginBottom: 12,
  },
  challengeProgress: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  progressBarBackground: {
    flex: 1,
    height: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 3,
  },
  progressBarFill: {
    width: '60%',
    height: '100%',
    backgroundColor: '#FFFFFF',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  progressSection: {
    padding: 20,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 26,
    fontFamily: 'Inter-Bold',
    color: '#000000',
  },
  sectionSubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
    marginTop: 4,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    backgroundColor: '#F2F2F7',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  viewAllText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#667eea',
  },
  progressGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  progressCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
  },
  progressRing: {
    marginBottom: 16,
  },
  progressCircle: {
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 4,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
  },
  progressNumber: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
  },
  progressLabel: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#000000',
    marginBottom: 4,
  },
  progressPercentage: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
  },
  featuredSection: {
    paddingVertical: 20,
  },
  featuredHeader: {
    paddingHorizontal: 20,
    marginBottom: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  featuredBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF3E0',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 6,
  },
  featuredBadgeText: {
    fontSize: 12,
    fontFamily: 'Inter-Bold',
    color: '#FF9500',
  },
  workoutScroll: {
    paddingLeft: 20,
  },
  workoutCard: {
    width: 280,
    height: 200,
    borderRadius: 20,
    marginRight: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
  workoutImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  workoutOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  workoutContent: {
    padding: 20,
  },
  workoutTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  workoutDetails: {
    flexDirection: 'row',
    gap: 16,
  },
  workoutDuration: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.9,
  },
  workoutIntensity: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.9,
  },
  quickActions: {
    padding: 20,
    paddingBottom: 40,
  },
  actionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginTop: 20,
  },
  actionCard: {
    width: '47%',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
  },
  actionIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  actionTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#000000',
    textAlign: 'center',
  },
});