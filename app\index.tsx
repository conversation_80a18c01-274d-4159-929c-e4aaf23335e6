import { useEffect } from 'react';
import { Redirect } from 'expo-router';
import { useAuth } from '@/contexts/AuthContext';
import { View, Text, StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, { FadeIn } from 'react-native-reanimated';

export default function Index() {
  const { user, isLoading } = useAuth();

  // Debug logging
  useEffect(() => {
    console.log('Index - User state:', user);
    console.log('Index - Loading state:', isLoading);
  }, [user, isLoading]);

  if (isLoading) {
    return (
      <View style={styles.container}>
        <LinearGradient
          colors={['#667eea', '#764ba2']}
          style={styles.gradient}>
          <Animated.View entering={FadeIn} style={styles.loadingContainer}>
            <Text style={styles.loadingText}>BulaFitX</Text>
            <Text style={styles.loadingSubtext}>Loading...</Text>
          </Animated.View>
        </LinearGradient>
      </View>
    );
  }

  if (!user) {
    console.log('No user found, redirecting to login');
    return <Redirect href="/(auth)/login" />;
  }

  // Route based on user role
  console.log('User found, routing based on role:', user.role);
  switch (user.role) {
    case 'trainer':
      return <Redirect href="/(trainer)" />;
    case 'admin':
      return <Redirect href="/(admin)" />;
    case 'nutritionist':
      return <Redirect href="/(nutritionist)" />;
    case 'physiotherapist':
      return <Redirect href="/(physiotherapist)" />;
    case 'content_creator':
      return <Redirect href="/(content-creator)" />;
    default:
      return <Redirect href="/(tabs)" />;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 32,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  loadingSubtext: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.8,
  },
});