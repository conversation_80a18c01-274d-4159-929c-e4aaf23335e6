import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, TextInput, StatusBar } from 'react-native';
import { Search, Filter, Clock, Users, Star, ChefHat, Leaf, Heart, Plus, Calendar } from 'lucide-react-native';
import Animated, { 
  FadeInDown, 
  FadeInRight, 
  FadeInUp,
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withRepeat,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';

const mealCategories = [
  {
    title: 'Traditional Fijian',
    description: 'Authentic recipes with local ingredients',
    image: 'https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?auto=compress&cs=tinysrgb&w=500',
    color: '#FF6B35',
    recipes: 24,
  },
  {
    title: 'Post-Workout',
    description: 'High-protein meals for recovery',
    image: 'https://images.pexels.com/photos/1640772/pexels-photo-1640772.jpeg?auto=compress&cs=tinysrgb&w=500',
    color: '#4ECDC4',
    recipes: 18,
  },
  {
    title: 'Quick & Healthy',
    description: 'Ready in 15 minutes or less',
    image: 'https://images.pexels.com/photos/1640774/pexels-photo-1640774.jpeg?auto=compress&cs=tinysrgb&w=500',
    color: '#45B7D1',
    recipes: 32,
  },
  {
    title: 'Plant-Based',
    description: 'Nutritious vegetarian options',
    image: 'https://images.pexels.com/photos/1640770/pexels-photo-1640770.jpeg?auto=compress&cs=tinysrgb&w=500',
    color: '#96CEB4',
    recipes: 28,
  },
];

const featuredRecipes = [
  {
    title: 'Kokoda (Fijian Ceviche)',
    chef: 'Chef Mere Ratunabuabua',
    cookTime: '20 min',
    difficulty: 'Easy',
    rating: 4.8,
    image: 'https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?auto=compress&cs=tinysrgb&w=500',
    calories: 280,
    protein: '25g',
    tags: ['Traditional', 'High Protein', 'Gluten Free'],
  },
  {
    title: 'Tropical Protein Bowl',
    chef: 'Chef Jone Vakacegu',
    cookTime: '15 min',
    difficulty: 'Easy',
    rating: 4.9,
    image: 'https://images.pexels.com/photos/1640772/pexels-photo-1640772.jpeg?auto=compress&cs=tinysrgb&w=500',
    calories: 420,
    protein: '32g',
    tags: ['Post-Workout', 'Quick', 'Superfood'],
  },
  {
    title: 'Cassava & Taro Curry',
    chef: 'Chef Salote Daurewa',
    cookTime: '35 min',
    difficulty: 'Medium',
    rating: 4.7,
    image: 'https://images.pexels.com/photos/1640774/pexels-photo-1640774.jpeg?auto=compress&cs=tinysrgb&w=500',
    calories: 350,
    protein: '18g',
    tags: ['Traditional', 'Vegan', 'Comfort Food'],
  },
];

const weeklyPlan = [
  { day: 'Mon', meal: 'Kokoda Bowl', calories: 380, image: 'https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?auto=compress&cs=tinysrgb&w=100' },
  { day: 'Tue', meal: 'Protein Smoothie', calories: 320, image: 'https://images.pexels.com/photos/1640772/pexels-photo-1640772.jpeg?auto=compress&cs=tinysrgb&w=100' },
  { day: 'Wed', meal: 'Taro Salad', calories: 290, image: 'https://images.pexels.com/photos/1640774/pexels-photo-1640774.jpeg?auto=compress&cs=tinysrgb&w=100' },
  { day: 'Thu', meal: 'Fish Curry', calories: 410, image: 'https://images.pexels.com/photos/1640770/pexels-photo-1640770.jpeg?auto=compress&cs=tinysrgb&w=100' },
  { day: 'Fri', meal: 'Fruit Bowl', calories: 250, image: 'https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?auto=compress&cs=tinysrgb&w=100' },
];

export default function MealsScreen() {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const backgroundAnimation = useSharedValue(0);

  useEffect(() => {
    // Background animation
    backgroundAnimation.value = withRepeat(
      withTiming(1, { duration: 8000 }),
      -1,
      true
    );
  }, []);

  const backgroundStyle = useAnimatedStyle(() => ({
    transform: [
      {
        translateY: backgroundAnimation.value * 20,
      },
    ],
    opacity: 0.1 + backgroundAnimation.value * 0.1,
  }));

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#FF6B35" />
      
      {/* Animated Background */}
      <View style={styles.backgroundContainer}>
        <LinearGradient
          colors={['#667eea', '#764ba2', '#f093fb', '#f5576c']}
          style={styles.backgroundGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
        
        {/* Floating Background Elements */}
        <Animated.View style={[styles.floatingElement, styles.element1, backgroundStyle]} />
        <Animated.View style={[styles.floatingElement, styles.element2, backgroundStyle]} />
        <Animated.View style={[styles.floatingElement, styles.element3, backgroundStyle]} />
        <Animated.View style={[styles.floatingElement, styles.element4, backgroundStyle]} />
        
        {/* Geometric Patterns */}
        <View style={styles.geometricPattern}>
          {[...Array(20)].map((_, index) => (
            <Animated.View
              key={index}
              entering={FadeInUp.delay(index * 100).springify()}
              style={[
                styles.geometricShape,
                {
                  left: `${(index % 5) * 20 + 10}%`,
                  top: `${Math.floor(index / 5) * 25 + 10}%`,
                  opacity: 0.1,
                  transform: [
                    { rotate: `${index * 18}deg` },
                    { scale: 0.5 + (index % 3) * 0.3 },
                  ],
                },
              ]}
            />
          ))}
        </View>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <LinearGradient
          colors={['rgba(255, 107, 53, 0.95)', 'rgba(247, 147, 30, 0.95)']}
          style={styles.header}>
          <View style={styles.headerContent}>
            <View>
              <Text style={styles.headerTitle}>Meal Planner</Text>
              <Text style={styles.headerSubtitle}>Fijian-inspired nutrition</Text>
            </View>
            <TouchableOpacity style={styles.calendarButton}>
              <Calendar size={24} color="#FFFFFF" />
            </TouchableOpacity>
          </View>

          <View style={styles.searchContainer}>
            <View style={styles.searchBar}>
              <Search size={20} color="#8E8E93" />
              <TextInput
                placeholder="Search recipes..."
                style={styles.searchInput}
                placeholderTextColor="#8E8E93"
              />
            </View>
            <TouchableOpacity style={styles.filterButton}>
              <Filter size={20} color="#FF6B35" />
            </TouchableOpacity>
          </View>
        </LinearGradient>

        <View style={styles.weeklyPlanSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>This Week's Plan</Text>
            <TouchableOpacity style={styles.editButton}>
              <Plus size={20} color="#FF6B35" />
              <Text style={styles.editButtonText}>Edit</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.weeklyScroll}>
            {weeklyPlan.map((item, index) => (
              <Animated.View
                key={item.day}
                entering={FadeInRight.delay(index * 100)}
                style={styles.dayCard}>
                <Text style={styles.dayText}>{item.day}</Text>
                <Image source={{ uri: item.image }} style={styles.dayImage} />
                <Text style={styles.dayMeal}>{item.meal}</Text>
                <Text style={styles.dayCalories}>{item.calories} cal</Text>
              </Animated.View>
            ))}
          </ScrollView>
        </View>

        <View style={styles.categoriesSection}>
          <Text style={styles.sectionTitle}>Categories</Text>
          <View style={styles.categoriesGrid}>
            {mealCategories.map((category, index) => (
              <Animated.View
                key={category.title}
                entering={FadeInDown.delay(index * 150)}
                style={styles.categoryCard}>
                <Image source={{ uri: category.image }} style={styles.categoryImage} />
                <LinearGradient
                  colors={['transparent', 'rgba(0,0,0,0.7)']}
                  style={styles.categoryOverlay}>
                  <View style={styles.categoryContent}>
                    <View style={[styles.categoryIcon, { backgroundColor: category.color }]}>
                      <ChefHat size={20} color="#FFFFFF" />
                    </View>
                    <Text style={styles.categoryTitle}>{category.title}</Text>
                    <Text style={styles.categoryDescription}>{category.description}</Text>
                    <Text style={styles.categoryRecipes}>{category.recipes} recipes</Text>
                  </View>
                </LinearGradient>
              </Animated.View>
            ))}
          </View>
        </View>

        <View style={styles.featuredSection}>
          <Text style={styles.sectionTitle}>Featured Recipes</Text>
          {featuredRecipes.map((recipe, index) => (
            <Animated.View
              key={recipe.title}
              entering={FadeInDown.delay(index * 200)}
              style={styles.recipeCard}>
              <Image source={{ uri: recipe.image }} style={styles.recipeImage} />
              <View style={styles.recipeContent}>
                <View style={styles.recipeHeader}>
                  <Text style={styles.recipeTitle}>{recipe.title}</Text>
                  <View style={styles.ratingContainer}>
                    <Star size={16} color="#FFD700" fill="#FFD700" />
                    <Text style={styles.rating}>{recipe.rating}</Text>
                  </View>
                </View>
                
                <Text style={styles.chefName}>by {recipe.chef}</Text>
                
                <View style={styles.recipeStats}>
                  <View style={styles.recipeStat}>
                    <Clock size={16} color="#8E8E93" />
                    <Text style={styles.recipeStatText}>{recipe.cookTime}</Text>
                  </View>
                  <View style={styles.recipeStat}>
                    <Heart size={16} color="#FF3B30" />
                    <Text style={styles.recipeStatText}>{recipe.calories} cal</Text>
                  </View>
                  <View style={styles.recipeStat}>
                    <Leaf size={16} color="#34C759" />
                    <Text style={styles.recipeStatText}>{recipe.protein}</Text>
                  </View>
                </View>

                <View style={styles.tagsContainer}>
                  {recipe.tags.map((tag, tagIndex) => (
                    <View key={tagIndex} style={styles.tag}>
                      <Text style={styles.tagText}>{tag}</Text>
                    </View>
                  ))}
                </View>

                <TouchableOpacity style={styles.cookButton}>
                  <ChefHat size={20} color="#FFFFFF" />
                  <Text style={styles.cookButtonText}>Start Cooking</Text>
                </TouchableOpacity>
              </View>
            </Animated.View>
          ))}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
  },
  backgroundGradient: {
    flex: 1,
  },
  floatingElement: {
    position: 'absolute',
    borderRadius: 100,
    opacity: 0.1,
  },
  element1: {
    width: 200,
    height: 200,
    backgroundColor: '#FF6B35',
    top: '10%',
    left: '-10%',
  },
  element2: {
    width: 150,
    height: 150,
    backgroundColor: '#4ECDC4',
    top: '30%',
    right: '-5%',
  },
  element3: {
    width: 120,
    height: 120,
    backgroundColor: '#45B7D1',
    bottom: '20%',
    left: '10%',
  },
  element4: {
    width: 180,
    height: 180,
    backgroundColor: '#96CEB4',
    bottom: '5%',
    right: '-8%',
  },
  geometricPattern: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  geometricShape: {
    position: 'absolute',
    width: 20,
    height: 20,
    backgroundColor: '#FFFFFF',
    borderRadius: 4,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 60,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  headerTitle: {
    fontSize: 34,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  headerSubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.9,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  calendarButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  searchBar: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 12,
    padding: 12,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#000000',
  },
  filterButton: {
    padding: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 12,
  },
  weeklyPlanSection: {
    padding: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 22,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  editButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FF6B35',
  },
  weeklyScroll: {
    marginHorizontal: -20,
    paddingHorizontal: 20,
  },
  dayCard: {
    width: 100,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 16,
    padding: 12,
    marginRight: 12,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  dayText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#8E8E93',
    marginBottom: 8,
  },
  dayImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginBottom: 8,
  },
  dayMeal: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#000000',
    textAlign: 'center',
    marginBottom: 4,
  },
  dayCalories: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
  },
  categoriesSection: {
    padding: 20,
  },
  categoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 15,
  },
  categoryCard: {
    width: '47%',
    height: 180,
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  categoryImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  categoryOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  categoryContent: {
    padding: 16,
  },
  categoryIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  categoryDescription: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.9,
    marginBottom: 4,
  },
  categoryRecipes: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.8,
  },
  featuredSection: {
    padding: 20,
    paddingBottom: 40,
  },
  recipeCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 16,
    marginBottom: 20,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  recipeImage: {
    width: '100%',
    height: 200,
  },
  recipeContent: {
    padding: 20,
  },
  recipeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  recipeTitle: {
    flex: 1,
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#000000',
    marginRight: 10,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  rating: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#000000',
  },
  chefName: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
    marginBottom: 16,
  },
  recipeStats: {
    flexDirection: 'row',
    gap: 20,
    marginBottom: 16,
  },
  recipeStat: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  recipeStatText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 20,
  },
  tag: {
    backgroundColor: '#F2F2F7',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  tagText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#8E8E93',
  },
  cookButton: {
    backgroundColor: '#FF6B35',
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  cookButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
});