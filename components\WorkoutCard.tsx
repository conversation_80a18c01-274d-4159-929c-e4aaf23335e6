import React, { useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  withRepeat,
  withSequence,
  interpolate,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { Play, Clock, Zap } from 'lucide-react-native';

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);
const AnimatedLinearGradient = Animated.createAnimatedComponent(LinearGradient);

interface WorkoutCardProps {
  title: string;
  duration: string;
  intensity: string;
  image: string;
  onPress: () => void;
  delay?: number;
}

export default function WorkoutCard({
  title,
  duration,
  intensity,
  image,
  onPress,
  delay = 0,
}: WorkoutCardProps) {
  const scale = useSharedValue(0);
  const opacity = useSharedValue(0);
  const translateY = useSharedValue(50);
  const glowOpacity = useSharedValue(0);
  const iconRotate = useSharedValue(0);

  useEffect(() => {
    // Entry animation
    scale.value = withSpring(1, { damping: 12 }, () => {
      // Glow effect after entry
      glowOpacity.value = withRepeat(
        withSequence(
          withTiming(0.6, { duration: 2000 }),
          withTiming(0.2, { duration: 2000 })
        ),
        -1,
        true
      );
    });

    opacity.value = withTiming(1, { duration: 800 });
    translateY.value = withSpring(0, { damping: 10 });

    // Icon rotation
    iconRotate.value = withRepeat(
      withTiming(360, { duration: 10000 }),
      -1,
      false
    );
  }, [delay]);

  const cardStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: scale.value },
      { translateY: translateY.value },
    ],
    opacity: opacity.value,
  }));

  const glowStyle = useAnimatedStyle(() => ({
    opacity: glowOpacity.value,
  }));

  const iconStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${iconRotate.value}deg` }],
  }));

  const handlePress = () => {
    scale.value = withSequence(
      withSpring(0.95, { damping: 10 }),
      withSpring(1, { damping: 8 })
    );
    onPress();
  };

  return (
    <AnimatedTouchableOpacity
      style={[styles.container, cardStyle]}
      onPress={handlePress}
      activeOpacity={0.9}>
      
      {/* Glow effect */}
      <Animated.View style={[styles.glowContainer, glowStyle]}>
        <LinearGradient
          colors={['rgba(102, 126, 234, 0.4)', 'transparent']}
          style={styles.glow}
        />
      </Animated.View>

      <Image source={{ uri: image }} style={styles.image} />
      
      <AnimatedLinearGradient
        colors={['transparent', 'rgba(0,0,0,0.8)']}
        style={styles.overlay}>
        
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>{title}</Text>
            <Animated.View style={[styles.playButton, iconStyle]}>
              <Play size={16} color="#FFFFFF" fill="#FFFFFF" />
            </Animated.View>
          </View>
          
          <View style={styles.details}>
            <View style={styles.detail}>
              <Clock size={14} color="#FFFFFF" />
              <Text style={styles.detailText}>{duration}</Text>
            </View>
            <View style={styles.detail}>
              <Zap size={14} color="#FFFFFF" />
              <Text style={styles.detailText}>{intensity}</Text>
            </View>
          </View>
        </View>
      </AnimatedLinearGradient>
    </AnimatedTouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    width: 280,
    height: 200,
    borderRadius: 20,
    marginRight: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 10,
  },
  glowContainer: {
    position: 'absolute',
    top: -10,
    left: -10,
    right: -10,
    bottom: -10,
    zIndex: -1,
  },
  glow: {
    flex: 1,
    borderRadius: 30,
  },
  image: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  overlay: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  content: {
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    flex: 1,
  },
  playButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 12,
  },
  details: {
    flexDirection: 'row',
    gap: 16,
  },
  detail: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  detailText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.9,
  },
});