import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity, StatusBar, Alert, ScrollView } from 'react-native';
import { Link, router } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { Eye, EyeOff, Mail, Lock, ArrowRight, User, Heart, Zap, Target, Calendar, Briefcase, Users, Stethoscope, Camera, Shield } from 'lucide-react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withRepeat,
  withSequence,
  withSpring,
  FadeInDown,
  FadeInUp,
  SlideInLeft,
  BounceIn,
} from 'react-native-reanimated';
import { useAuth } from '@/contexts/AuthContext';
import { UserRole } from '@/types/user';

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);
const AnimatedScrollView = Animated.createAnimatedComponent(ScrollView);

export default function SignupScreen() {
  const { signup } = useAuth();
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    role: 'client' as UserRole,
    age: '',
    fitnessGoal: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);

  const backgroundAnimation = useSharedValue(0);
  const pulseAnimation = useSharedValue(1);
  const floatingY = useSharedValue(0);
  const shimmerX = useSharedValue(-200);

  const userRoles = [
    { id: 'client', label: 'Client/Member', icon: User, color: '#007AFF', description: 'Access workouts, track progress, join challenges' },
    { id: 'trainer', label: 'Trainer/Coach', icon: Zap, color: '#FF9500', description: 'Create workouts, manage clients, provide coaching' },
    { id: 'nutritionist', label: 'Nutritionist', icon: Heart, color: '#34C759', description: 'Create meal plans, provide dietary guidance' },
    { id: 'physiotherapist', label: 'Physiotherapist', icon: Stethoscope, color: '#FF3B30', description: 'Rehabilitation programs, injury prevention' },
    { id: 'content_creator', label: 'Content Creator', icon: Camera, color: '#AF52DE', description: 'Create fitness content, build community' },
    { id: 'admin', label: 'Administrator', icon: Shield, color: '#8E8E93', description: 'Platform management and oversight' },
  ];

  const fitnessGoals = [
    { id: 'weight_loss', label: 'Weight Loss', icon: Target, color: '#FF3B30' },
    { id: 'muscle_gain', label: 'Muscle Gain', icon: Zap, color: '#007AFF' },
    { id: 'endurance', label: 'Endurance', icon: Heart, color: '#34C759' },
    { id: 'general_fitness', label: 'General Fitness', icon: User, color: '#FF9500' },
  ];

  useEffect(() => {
    // Background animation
    backgroundAnimation.value = withRepeat(
      withTiming(1, { duration: 8000 }),
      -1,
      true
    );

    // Pulse animation for signup button
    pulseAnimation.value = withRepeat(
      withSequence(
        withSpring(1.02, { damping: 8 }),
        withSpring(1, { damping: 8 })
      ),
      -1,
      true
    );

    // Floating animation for elements
    floatingY.value = withRepeat(
      withSequence(
        withTiming(-10, { duration: 3000 }),
        withTiming(10, { duration: 3000 })
      ),
      -1,
      true
    );

    // Shimmer effect
    shimmerX.value = withRepeat(
      withTiming(400, { duration: 2000 }),
      -1,
      false
    );
  }, []);

  const backgroundStyle = useAnimatedStyle(() => ({
    transform: [
      {
        translateY: backgroundAnimation.value * 20,
      },
    ],
    opacity: 0.1 + backgroundAnimation.value * 0.1,
  }));

  const pulseStyle = useAnimatedStyle(() => ({
    transform: [{ scale: pulseAnimation.value }],
  }));

  const floatingStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: floatingY.value }],
  }));

  const shimmerStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: shimmerX.value }],
  }));

  const updateFormData = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleNext = () => {
    if (currentStep === 1) {
      if (!formData.firstName || !formData.lastName || !formData.email) {
        Alert.alert('Error', 'Please fill in all required fields');
        return;
      }
      setCurrentStep(2);
    } else if (currentStep === 2) {
      if (!formData.password || !formData.confirmPassword) {
        Alert.alert('Error', 'Please fill in all password fields');
        return;
      }
      if (formData.password !== formData.confirmPassword) {
        Alert.alert('Error', 'Passwords do not match');
        return;
      }
      setCurrentStep(3);
    } else if (currentStep === 3) {
      if (!formData.role) {
        Alert.alert('Error', 'Please select your role');
        return;
      }
      if (formData.role === 'client') {
        setCurrentStep(4);
      } else {
        handleSignup();
      }
    }
  };

  const handleSignup = async () => {
    if (formData.role === 'client' && (!formData.age || !formData.fitnessGoal)) {
      Alert.alert('Error', 'Please complete all fields');
      return;
    }

    setIsLoading(true);
    
    try {
      await signup({
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        password: formData.password,
        role: formData.role,
        age: formData.age ? parseInt(formData.age) : undefined,
        fitnessGoal: formData.fitnessGoal,
      });
      
      Alert.alert('Success', 'Account created successfully!', [
        { text: 'OK', onPress: () => router.replace('/') }
      ]);
    } catch (error) {
      Alert.alert('Error', 'Failed to create account. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const renderStep1 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Personal Information</Text>
      <Text style={styles.stepSubtitle}>Let's get to know you better</Text>

      <View style={styles.inputRow}>
        <View style={[styles.inputContainer, { flex: 1, marginRight: 8 }]}>
          <View style={styles.inputIcon}>
            <User size={20} color="#8E8E93" />
          </View>
          <TextInput
            style={styles.input}
            placeholder="First Name"
            placeholderTextColor="#8E8E93"
            value={formData.firstName}
            onChangeText={(value) => updateFormData('firstName', value)}
            autoCapitalize="words"
          />
        </View>

        <View style={[styles.inputContainer, { flex: 1, marginLeft: 8 }]}>
          <View style={styles.inputIcon}>
            <User size={20} color="#8E8E93" />
          </View>
          <TextInput
            style={styles.input}
            placeholder="Last Name"
            placeholderTextColor="#8E8E93"
            value={formData.lastName}
            onChangeText={(value) => updateFormData('lastName', value)}
            autoCapitalize="words"
          />
        </View>
      </View>

      <View style={styles.inputContainer}>
        <View style={styles.inputIcon}>
          <Mail size={20} color="#8E8E93" />
        </View>
        <TextInput
          style={styles.input}
          placeholder="Email address"
          placeholderTextColor="#8E8E93"
          value={formData.email}
          onChangeText={(value) => updateFormData('email', value)}
          keyboardType="email-address"
          autoCapitalize="none"
          autoComplete="email"
        />
      </View>
    </View>
  );

  const renderStep2 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Secure Your Account</Text>
      <Text style={styles.stepSubtitle}>Create a strong password</Text>

      <View style={styles.inputContainer}>
        <View style={styles.inputIcon}>
          <Lock size={20} color="#8E8E93" />
        </View>
        <TextInput
          style={styles.input}
          placeholder="Password"
          placeholderTextColor="#8E8E93"
          value={formData.password}
          onChangeText={(value) => updateFormData('password', value)}
          secureTextEntry={!showPassword}
          autoComplete="new-password"
        />
        <TouchableOpacity
          style={styles.eyeIcon}
          onPress={() => setShowPassword(!showPassword)}>
          {showPassword ? (
            <EyeOff size={20} color="#8E8E93" />
          ) : (
            <Eye size={20} color="#8E8E93" />
          )}
        </TouchableOpacity>
      </View>

      <View style={styles.inputContainer}>
        <View style={styles.inputIcon}>
          <Lock size={20} color="#8E8E93" />
        </View>
        <TextInput
          style={styles.input}
          placeholder="Confirm Password"
          placeholderTextColor="#8E8E93"
          value={formData.confirmPassword}
          onChangeText={(value) => updateFormData('confirmPassword', value)}
          secureTextEntry={!showConfirmPassword}
          autoComplete="new-password"
        />
        <TouchableOpacity
          style={styles.eyeIcon}
          onPress={() => setShowConfirmPassword(!showConfirmPassword)}>
          {showConfirmPassword ? (
            <EyeOff size={20} color="#8E8E93" />
          ) : (
            <Eye size={20} color="#8E8E93" />
          )}
        </TouchableOpacity>
      </View>

      <View style={styles.passwordStrength}>
        <Text style={styles.passwordStrengthText}>
          Password should be at least 8 characters long
        </Text>
      </View>
    </View>
  );

  const renderStep3 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Choose Your Role</Text>
      <Text style={styles.stepSubtitle}>Select how you'll use BulaFitX</Text>

      <View style={styles.rolesContainer}>
        {userRoles.map((role, index) => (
          <Animated.View
            key={role.id}
            entering={BounceIn.delay(index * 100)}>
            <TouchableOpacity
              style={[
                styles.roleCard,
                formData.role === role.id && styles.roleCardSelected
              ]}
              onPress={() => updateFormData('role', role.id)}>
              <LinearGradient
                colors={formData.role === role.id 
                  ? [role.color, role.color + '80'] 
                  : ['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)']
                }
                style={styles.roleGradient}>
                <View style={[styles.roleIcon, { backgroundColor: role.color }]}>
                  <role.icon size={24} color="#FFFFFF" />
                </View>
                <Text style={[
                  styles.roleLabel,
                  formData.role === role.id && styles.roleLabelSelected
                ]}>
                  {role.label}
                </Text>
                <Text style={[
                  styles.roleDescription,
                  formData.role === role.id && styles.roleDescriptionSelected
                ]}>
                  {role.description}
                </Text>
              </LinearGradient>
            </TouchableOpacity>
          </Animated.View>
        ))}
      </View>
    </View>
  );

  const renderStep4 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Fitness Profile</Text>
      <Text style={styles.stepSubtitle}>Help us personalize your experience</Text>

      <View style={styles.inputContainer}>
        <View style={styles.inputIcon}>
          <Calendar size={20} color="#8E8E93" />
        </View>
        <TextInput
          style={styles.input}
          placeholder="Age"
          placeholderTextColor="#8E8E93"
          value={formData.age}
          onChangeText={(value) => updateFormData('age', value)}
          keyboardType="numeric"
          maxLength={2}
        />
      </View>

      <Text style={styles.goalTitle}>What's your primary fitness goal?</Text>
      <View style={styles.goalsContainer}>
        {fitnessGoals.map((goal, index) => (
          <Animated.View
            key={goal.id}
            entering={BounceIn.delay(index * 100)}>
            <TouchableOpacity
              style={[
                styles.goalCard,
                formData.fitnessGoal === goal.id && styles.goalCardSelected
              ]}
              onPress={() => updateFormData('fitnessGoal', goal.id)}>
              <LinearGradient
                colors={formData.fitnessGoal === goal.id 
                  ? [goal.color, goal.color + '80'] 
                  : ['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)']
                }
                style={styles.goalGradient}>
                <View style={[styles.goalIcon, { backgroundColor: goal.color }]}>
                  <goal.icon size={24} color="#FFFFFF" />
                </View>
                <Text style={[
                  styles.goalLabel,
                  formData.fitnessGoal === goal.id && styles.goalLabelSelected
                ]}>
                  {goal.label}
                </Text>
              </LinearGradient>
            </TouchableOpacity>
          </Animated.View>
        ))}
      </View>
    </View>
  );

  const totalSteps = formData.role === 'client' ? 4 : 3;

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#667eea" />
      
      {/* Animated Background */}
      <View style={styles.backgroundContainer}>
        <LinearGradient
          colors={['#667eea', '#764ba2', '#f093fb', '#f5576c']}
          style={styles.backgroundGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
        
        {/* Floating Background Elements */}
        <Animated.View style={[styles.floatingElement, styles.element1, backgroundStyle]} />
        <Animated.View style={[styles.floatingElement, styles.element2, backgroundStyle]} />
        <Animated.View style={[styles.floatingElement, styles.element3, backgroundStyle]} />
        <Animated.View style={[styles.floatingElement, styles.element4, backgroundStyle]} />
        
        {/* Geometric Patterns */}
        <View style={styles.geometricPattern}>
          {[...Array(20)].map((_, index) => (
            <Animated.View
              key={index}
              entering={FadeInUp.delay(index * 100).springify()}
              style={[
                styles.geometricShape,
                {
                  left: `${(index % 5) * 20 + 10}%`,
                  top: `${Math.floor(index / 5) * 25 + 10}%`,
                  opacity: 0.1,
                  transform: [
                    { rotate: `${index * 18}deg` },
                    { scale: 0.5 + (index % 3) * 0.3 },
                  ],
                },
              ]}
            />
          ))}
        </View>
      </View>

      <AnimatedScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        entering={SlideInLeft.delay(200)}>
        
        {/* Header Section */}
        <Animated.View 
          entering={FadeInDown.delay(200)}
          style={[styles.header, floatingStyle]}>
          <LinearGradient
            colors={['rgba(255, 255, 255, 0.95)', 'rgba(255, 255, 255, 0.85)']}
            style={styles.logoContainer}>
            <View style={styles.logoIcon}>
              <Heart size={32} color="#667eea" fill="#667eea" />
            </View>
            <Animated.View style={[styles.shimmer, shimmerStyle]} />
          </LinearGradient>
          <Text style={styles.title}>Join BulaFitX</Text>
          <Text style={styles.subtitle}>Start your fitness transformation</Text>
          
          {/* Progress Indicator */}
          <View style={styles.progressContainer}>
            {Array.from({ length: totalSteps }, (_, i) => i + 1).map((step) => (
              <View
                key={step}
                style={[
                  styles.progressDot,
                  currentStep >= step && styles.progressDotActive
                ]}
              />
            ))}
          </View>
        </Animated.View>

        {/* Form Section */}
        <View style={styles.formContainer}>
          {currentStep === 1 && renderStep1()}
          {currentStep === 2 && renderStep2()}
          {currentStep === 3 && renderStep3()}
          {currentStep === 4 && renderStep4()}

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            {currentStep > 1 && (
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => setCurrentStep(currentStep - 1)}>
                <Text style={styles.backButtonText}>Back</Text>
              </TouchableOpacity>
            )}

            <AnimatedTouchableOpacity
              style={[styles.actionButton, pulseStyle, currentStep > 1 && { flex: 1, marginLeft: 12 }]}
              onPress={currentStep === totalSteps ? handleSignup : handleNext}
              disabled={isLoading}>
              <LinearGradient
                colors={['#667eea', '#764ba2']}
                style={styles.actionGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}>
                {isLoading ? (
                  <Text style={styles.actionButtonText}>Creating Account...</Text>
                ) : (
                  <>
                    <Text style={styles.actionButtonText}>
                      {currentStep === totalSteps ? 'Create Account' : 'Next'}
                    </Text>
                    <ArrowRight size={20} color="#FFFFFF" />
                  </>
                )}
              </LinearGradient>
            </AnimatedTouchableOpacity>
          </View>

          {/* Login Link */}
          <View style={styles.loginContainer}>
            <Text style={styles.loginText}>Already have an account? </Text>
            <Link href="/(auth)/login" asChild>
              <TouchableOpacity>
                <Text style={styles.loginLink}>Sign In</Text>
              </TouchableOpacity>
            </Link>
          </View>
        </View>
      </AnimatedScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
  },
  backgroundGradient: {
    flex: 1,
  },
  floatingElement: {
    position: 'absolute',
    borderRadius: 100,
    opacity: 0.1,
  },
  element1: {
    width: 200,
    height: 200,
    backgroundColor: '#FF6B35',
    top: '10%',
    left: '-10%',
  },
  element2: {
    width: 150,
    height: 150,
    backgroundColor: '#4ECDC4',
    top: '30%',
    right: '-5%',
  },
  element3: {
    width: 120,
    height: 120,
    backgroundColor: '#45B7D1',
    bottom: '20%',
    left: '10%',
  },
  element4: {
    width: 180,
    height: 180,
    backgroundColor: '#96CEB4',
    bottom: '5%',
    right: '-8%',
  },
  geometricPattern: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  geometricShape: {
    position: 'absolute',
    width: 20,
    height: 20,
    backgroundColor: '#FFFFFF',
    borderRadius: 4,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 80,
    paddingBottom: 20,
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    overflow: 'hidden',
    position: 'relative',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
  logoIcon: {
    zIndex: 2,
  },
  shimmer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    width: 20,
  },
  title: {
    fontSize: 32,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 8,
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.9,
    textAlign: 'center',
    marginBottom: 20,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  progressContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  progressDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  progressDotActive: {
    backgroundColor: '#FFFFFF',
  },
  formContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    padding: 24,
    marginTop: 20,
    minHeight: 500,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -8 },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    elevation: 12,
  },
  stepContainer: {
    marginBottom: 24,
  },
  stepTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#000000',
    marginBottom: 8,
    textAlign: 'center',
  },
  stepSubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
    textAlign: 'center',
    marginBottom: 24,
  },
  inputRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F2F2F7',
    borderRadius: 16,
    marginBottom: 16,
    paddingHorizontal: 16,
    height: 56,
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#000000',
  },
  eyeIcon: {
    padding: 4,
  },
  passwordStrength: {
    marginTop: -8,
    marginBottom: 16,
  },
  passwordStrengthText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
    textAlign: 'center',
  },
  rolesContainer: {
    gap: 12,
    marginBottom: 24,
  },
  roleCard: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  roleCardSelected: {
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  roleGradient: {
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    minHeight: 80,
  },
  roleIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  roleLabel: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#000000',
    marginBottom: 4,
    flex: 1,
  },
  roleLabelSelected: {
    color: '#FFFFFF',
  },
  roleDescription: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
    flex: 2,
  },
  roleDescriptionSelected: {
    color: 'rgba(255, 255, 255, 0.9)',
  },
  goalTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#000000',
    marginBottom: 16,
    textAlign: 'center',
  },
  goalsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 24,
  },
  goalCard: {
    width: '47%',
    borderRadius: 16,
    overflow: 'hidden',
  },
  goalCardSelected: {
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  goalGradient: {
    padding: 16,
    alignItems: 'center',
    minHeight: 100,
    justifyContent: 'center',
  },
  goalIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  goalLabel: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#000000',
    textAlign: 'center',
  },
  goalLabelSelected: {
    color: '#FFFFFF',
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  backButton: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 16,
    backgroundColor: '#F2F2F7',
  },
  backButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#8E8E93',
  },
  actionButton: {
    flex: 1,
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  actionGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    gap: 8,
  },
  actionButtonText: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loginText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
  },
  loginLink: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#667eea',
  },
});