import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, StatusBar, Image } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BookOpen, FileText, Video, Download, Star, Clock, Users, Plus } from 'lucide-react-native';
import Animated, { FadeInDown, FadeInRight } from 'react-native-reanimated';

export default function ResourcesScreen() {
  const resourceCategories = [
    { name: 'Meal Plans', icon: FileText, count: 24, color: '#34C759' },
    { name: 'Educational Videos', icon: Video, count: 18, color: '#007AFF' },
    { name: 'Recipe Collections', icon: BookOpen, count: 32, color: '#FF9500' },
    { name: 'Client Handouts', icon: Download, count: 15, color: '#AF52DE' },
  ];

  const featuredResources = [
    {
      id: '1',
      title: 'Complete Guide to Macronutrients',
      type: 'PDF Guide',
      description: 'Comprehensive guide covering proteins, carbs, and fats for optimal health',
      downloads: 156,
      rating: 4.9,
      image: 'https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?auto=compress&cs=tinysrgb&w=300',
      duration: '15 min read',
    },
    {
      id: '2',
      title: 'Meal Prep Mastery Video Series',
      type: 'Video Course',
      description: 'Step-by-step video tutorials for efficient meal preparation',
      downloads: 89,
      rating: 4.8,
      image: 'https://images.pexels.com/photos/1640772/pexels-photo-1640772.jpeg?auto=compress&cs=tinysrgb&w=300',
      duration: '2.5 hours',
    },
    {
      id: '3',
      title: 'Plant-Based Recipe Collection',
      type: 'Recipe Book',
      description: '50+ delicious and nutritious plant-based recipes for every meal',
      downloads: 203,
      rating: 4.7,
      image: 'https://images.pexels.com/photos/1640774/pexels-photo-1640774.jpeg?auto=compress&cs=tinysrgb&w=300',
      duration: '30 min read',
    },
  ];

  const recentUploads = [
    { title: 'Hydration Guidelines Handout', type: 'PDF', date: '2 days ago' },
    { title: 'Portion Control Visual Guide', type: 'Infographic', date: '1 week ago' },
    { title: 'Supplement Safety Video', type: 'Video', date: '2 weeks ago' },
  ];

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#34C759" />
      
      <LinearGradient
        colors={['rgba(52, 199, 89, 0.95)', 'rgba(48, 209, 88, 0.95)']}
        style={styles.header}>
        <Text style={styles.headerTitle}>Resources</Text>
        <Text style={styles.headerSubtitle}>Educational content & tools</Text>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.categoriesSection}>
          <Text style={styles.sectionTitle}>Resource Categories</Text>
          <View style={styles.categoriesGrid}>
            {resourceCategories.map((category, index) => (
              <Animated.View
                key={category.name}
                entering={FadeInDown.delay(index * 100)}
                style={styles.categoryCard}>
                <TouchableOpacity style={styles.categoryButton}>
                  <View style={[styles.categoryIcon, { backgroundColor: category.color }]}>
                    <category.icon size={24} color="#FFFFFF" />
                  </View>
                  <Text style={styles.categoryName}>{category.name}</Text>
                  <Text style={styles.categoryCount}>{category.count} items</Text>
                </TouchableOpacity>
              </Animated.View>
            ))}
          </View>
        </View>

        <View style={styles.featuredSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Featured Resources</Text>
            <TouchableOpacity style={styles.addButton}>
              <Plus size={20} color="#34C759" />
            </TouchableOpacity>
          </View>

          {featuredResources.map((resource, index) => (
            <Animated.View
              key={resource.id}
              entering={FadeInRight.delay(300 + index * 150)}
              style={styles.resourceCard}>
              <Image source={{ uri: resource.image }} style={styles.resourceImage} />
              
              <View style={styles.resourceContent}>
                <View style={styles.resourceHeader}>
                  <Text style={styles.resourceTitle}>{resource.title}</Text>
                  <View style={styles.typeBadge}>
                    <Text style={styles.typeText}>{resource.type}</Text>
                  </View>
                </View>
                
                <Text style={styles.resourceDescription}>{resource.description}</Text>
                
                <View style={styles.resourceStats}>
                  <View style={styles.resourceStat}>
                    <Clock size={16} color="#8E8E93" />
                    <Text style={styles.resourceStatText}>{resource.duration}</Text>
                  </View>
                  <View style={styles.resourceStat}>
                    <Download size={16} color="#8E8E93" />
                    <Text style={styles.resourceStatText}>{resource.downloads} downloads</Text>
                  </View>
                  <View style={styles.resourceStat}>
                    <Star size={16} color="#FFD700" fill="#FFD700" />
                    <Text style={styles.resourceStatText}>{resource.rating}</Text>
                  </View>
                </View>

                <View style={styles.resourceActions}>
                  <TouchableOpacity style={styles.previewButton}>
                    <Text style={styles.previewButtonText}>Preview</Text>
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.shareButton}>
                    <Text style={styles.shareButtonText}>Share with Client</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </Animated.View>
          ))}
        </View>

        <View style={styles.recentSection}>
          <Text style={styles.sectionTitle}>Recent Uploads</Text>
          {recentUploads.map((upload, index) => (
            <Animated.View
              key={upload.title}
              entering={FadeInDown.delay(600 + index * 100)}
              style={styles.uploadCard}>
              <View style={styles.uploadIcon}>
                {upload.type === 'PDF' && <FileText size={20} color="#FF3B30" />}
                {upload.type === 'Infographic' && <BookOpen size={20} color="#34C759" />}
                {upload.type === 'Video' && <Video size={20} color="#007AFF" />}
              </View>
              <View style={styles.uploadInfo}>
                <Text style={styles.uploadTitle}>{upload.title}</Text>
                <Text style={styles.uploadDate}>{upload.date}</Text>
              </View>
              <TouchableOpacity style={styles.uploadAction}>
                <Text style={styles.uploadActionText}>View</Text>
              </TouchableOpacity>
            </Animated.View>
          ))}
        </View>

        <View style={styles.quickActionsSection}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.actionsGrid}>
            <TouchableOpacity style={styles.actionCard}>
              <LinearGradient
                colors={['#34C759', '#30D158']}
                style={styles.actionGradient}>
                <Plus size={24} color="#FFFFFF" />
                <Text style={styles.actionText}>Upload Resource</Text>
              </LinearGradient>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.actionCard}>
              <LinearGradient
                colors={['#007AFF', '#5AC8FA']}
                style={styles.actionGradient}>
                <FileText size={24} color="#FFFFFF" />
                <Text style={styles.actionText}>Create Handout</Text>
              </LinearGradient>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.actionCard}>
              <LinearGradient
                colors={['#FF9500', '#FFCC02']}
                style={styles.actionGradient}>
                <Video size={24} color="#FFFFFF" />
                <Text style={styles.actionText}>Record Video</Text>
              </LinearGradient>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.actionCard}>
              <LinearGradient
                colors={['#AF52DE', '#BF5AF2']}
                style={styles.actionGradient}>
                <Users size={24} color="#FFFFFF" />
                <Text style={styles.actionText}>Share Library</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    padding: 20,
    paddingTop: 60,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  headerTitle: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.9,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  categoriesSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#000000',
    marginBottom: 16,
  },
  categoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  categoryCard: {
    width: '47%',
  },
  categoryButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  categoryIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  categoryName: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#000000',
    textAlign: 'center',
    marginBottom: 4,
  },
  categoryCount: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
  },
  featuredSection: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  addButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F2F2F7',
    justifyContent: 'center',
    alignItems: 'center',
  },
  resourceCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    marginBottom: 16,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
  },
  resourceImage: {
    width: '100%',
    height: 140,
  },
  resourceContent: {
    padding: 16,
  },
  resourceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  resourceTitle: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#000000',
    marginRight: 12,
  },
  typeBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: '#F2F2F7',
  },
  typeText: {
    fontSize: 10,
    fontFamily: 'Inter-SemiBold',
    color: '#8E8E93',
  },
  resourceDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
    marginBottom: 12,
    lineHeight: 20,
  },
  resourceStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  resourceStat: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  resourceStatText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
  },
  resourceActions: {
    flexDirection: 'row',
    gap: 8,
  },
  previewButton: {
    flex: 1,
    paddingVertical: 10,
    borderRadius: 12,
    backgroundColor: '#F2F2F7',
    alignItems: 'center',
  },
  previewButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#8E8E93',
  },
  shareButton: {
    flex: 1,
    paddingVertical: 10,
    borderRadius: 12,
    backgroundColor: '#34C759',
    alignItems: 'center',
  },
  shareButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  recentSection: {
    marginBottom: 24,
  },
  uploadCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
  },
  uploadIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F2F2F7',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  uploadInfo: {
    flex: 1,
  },
  uploadTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#000000',
    marginBottom: 2,
  },
  uploadDate: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
  },
  uploadAction: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    backgroundColor: '#F2F2F7',
  },
  uploadActionText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#34C759',
  },
  quickActionsSection: {
    marginBottom: 40,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  actionCard: {
    width: '47%',
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  actionGradient: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 100,
  },
  actionText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
    marginTop: 8,
    textAlign: 'center',
  },
});