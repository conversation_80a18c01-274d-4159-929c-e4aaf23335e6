import { useAuth } from '@/contexts/AuthContext';
import { ROLE_PERMISSIONS } from '@/types/user';

export function usePermissions() {
  const { user } = useAuth();

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    
    const userPermissions = ROLE_PERMISSIONS[user.role] || [];
    return userPermissions.includes(permission);
  };

  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissions.some(permission => hasPermission(permission));
  };

  const hasAllPermissions = (permissions: string[]): boolean => {
    return permissions.every(permission => hasPermission(permission));
  };

  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    userRole: user?.role,
    isAdmin: user?.role === 'admin',
    isTrainer: user?.role === 'trainer',
    isClient: user?.role === 'client',
    isNutritionist: user?.role === 'nutritionist',
    isPhysiotherapist: user?.role === 'physiotherapist',
    isContentCreator: user?.role === 'content_creator',
  };
}