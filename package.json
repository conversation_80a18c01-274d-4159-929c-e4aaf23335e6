{"name": "bulafitx", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"start": "expo start", "dev": "expo start", "build:web": "expo export --platform web", "lint": "expo lint"}, "dependencies": {"@expo-google-fonts/inter": "^0.2.3", "@expo/vector-icons": "^14.1.0", "expo": "^53.0.17", "expo-constants": "~17.1.7", "expo-font": "~13.3.2", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-router": "~5.1.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.10", "lucide-react-native": "^0.475.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-native": "^0.79.5", "react-native-gesture-handler": "^2.24.0", "react-native-reanimated": "^3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.11.1", "react-native-svg": "^15.11.2", "react-native-web": "^0.20.0", "undefined": "\\"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "^19.0.10", "typescript": "^5.8.3"}}