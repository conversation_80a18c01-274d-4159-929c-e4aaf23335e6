import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, TextInput, StatusBar } from 'react-native';
import { Search, MessageCircle, Heart, Share2, Trophy, Users, MapPin, Calendar, Plus, Star, Target, Award } from 'lucide-react-native';
import Animated, { 
  FadeInDown, 
  FadeInRight, 
  FadeInUp,
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withRepeat,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';

const communityStats = [
  { label: 'Active Members', value: '2,847', icon: Users, color: '#007AFF' },
  { label: 'Challenges', value: '24', icon: Trophy, color: '#FF9500' },
  { label: 'Villages', value: '12', icon: MapPin, color: '#34C759' },
  { label: 'Projects Funded', value: '8', icon: Award, color: '#FF3B30' },
];

const activeChallenges = [
  {
    title: '30-Day Fitness Challenge',
    description: 'Complete daily workouts and track your progress',
    participants: 1247,
    daysLeft: 12,
    reward: '500 points',
    image: 'https://images.pexels.com/photos/416778/pexels-photo-416778.jpeg?auto=compress&cs=tinysrgb&w=500',
    progress: 75,
    category: 'Fitness',
  },
  {
    title: 'Beach Cleanup Warriors',
    description: 'Join weekly beach cleanups across Fiji',
    participants: 324,
    daysLeft: 5,
    reward: '300 points',
    image: 'https://images.pexels.com/photos/2547565/pexels-photo-2547565.jpeg?auto=compress&cs=tinysrgb&w=500',
    progress: 60,
    category: 'Eco-Fitness',
  },
  {
    title: 'Traditional Dance Marathon',
    description: 'Learn and perform traditional Fijian dances',
    participants: 892,
    daysLeft: 18,
    reward: '400 points',
    image: 'https://images.pexels.com/photos/1701194/pexels-photo-1701194.jpeg?auto=compress&cs=tinysrgb&w=500',
    progress: 45,
    category: 'Cultural',
  },
];

const communityPosts = [
  {
    id: 1,
    user: {
      name: 'Mere Ratunabuabua',
      avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=100',
      location: 'Suva, Fiji',
      level: 'Fitness Warrior',
    },
    content: 'Just completed my morning beach run! The sunrise was absolutely breathtaking. Nothing beats starting the day with nature\'s beauty and a good workout! 🌅🏃‍♀️',
    image: 'https://images.pexels.com/photos/416978/pexels-photo-416978.jpeg?auto=compress&cs=tinysrgb&w=500',
    likes: 127,
    comments: 23,
    shares: 8,
    timeAgo: '2h ago',
    workout: 'Beach Run - 5.2km',
    calories: 420,
  },
  {
    id: 2,
    user: {
      name: 'Jone Vakacegu',
      avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=100',
      location: 'Nadi, Fiji',
      level: 'Community Leader',
    },
    content: 'Our village gym project is 80% funded! Thanks to everyone who participated in the fitness challenges. Together we\'re building a healthier community! 💪🏽',
    image: 'https://images.pexels.com/photos/1552252/pexels-photo-1552252.jpeg?auto=compress&cs=tinysrgb&w=500',
    likes: 234,
    comments: 45,
    shares: 18,
    timeAgo: '4h ago',
    achievement: 'Community Builder',
    points: 150,
  },
  {
    id: 3,
    user: {
      name: 'Salote Daurewa',
      avatar: 'https://images.pexels.com/photos/1239288/pexels-photo-1239288.jpeg?auto=compress&cs=tinysrgb&w=100',
      location: 'Lautoka, Fiji',
      level: 'Eco Warrior',
    },
    content: 'Coconut tree climbing session was intense today! This traditional Fijian exercise is no joke - full body workout that connects us to our roots 🥥🌴',
    image: 'https://images.pexels.com/photos/1001682/pexels-photo-1001682.jpeg?auto=compress&cs=tinysrgb&w=500',
    likes: 89,
    comments: 12,
    shares: 5,
    timeAgo: '6h ago',
    workout: 'Coconut Climbing - 45min',
    calories: 380,
  },
];

const leaderboard = [
  { rank: 1, name: 'Mere Ratunabuabua', points: 2847, avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=100', badge: '👑' },
  { rank: 2, name: 'Jone Vakacegu', points: 2634, avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=100', badge: '🥈' },
  { rank: 3, name: 'Salote Daurewa', points: 2456, avatar: 'https://images.pexels.com/photos/1239288/pexels-photo-1239288.jpeg?auto=compress&cs=tinysrgb&w=100', badge: '🥉' },
  { rank: 4, name: 'Tevita Ratunabuabua', points: 2234, avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=100', badge: '⭐' },
  { rank: 5, name: 'Ana Vakacegu', points: 2156, avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=100', badge: '⭐' },
];

export default function CommunityScreen() {
  const [activeTab, setActiveTab] = useState('feed');

  const renderFeed = () => (
    <View style={styles.feedSection}>
      <View style={styles.createPostContainer}>
        <Image
          source={{ uri: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=100' }}
          style={styles.userAvatar}
        />
        <TouchableOpacity style={styles.createPostInput}>
          <Text style={styles.createPostPlaceholder}>Share your fitness journey...</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.createPostButton}>
          <Plus size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      {communityPosts.map((post, index) => (
        <Animated.View
          key={post.id}
          entering={FadeInDown.delay(index * 200)}
          style={styles.postCard}>
          <View style={styles.postHeader}>
            <Image source={{ uri: post.user.avatar }} style={styles.postAvatar} />
            <View style={styles.postUserInfo}>
              <Text style={styles.postUserName}>{post.user.name}</Text>
              <View style={styles.postMetaInfo}>
                <MapPin size={12} color="#8E8E93" />
                <Text style={styles.postLocation}>{post.user.location}</Text>
                <Text style={styles.postTime}>• {post.timeAgo}</Text>
              </View>
              <View style={styles.userBadge}>
                <Star size={12} color="#FFD700" fill="#FFD700" />
                <Text style={styles.userLevel}>{post.user.level}</Text>
              </View>
            </View>
          </View>

          <Text style={styles.postContent}>{post.content}</Text>

          {post.image && (
            <Image source={{ uri: post.image }} style={styles.postImage} />
          )}

          {post.workout && (
            <View style={styles.workoutInfo}>
              <View style={styles.workoutDetail}>
                <Target size={16} color="#007AFF" />
                <Text style={styles.workoutText}>{post.workout}</Text>
              </View>
              <Text style={styles.caloriesText}>{post.calories} calories</Text>
            </View>
          )}

          {post.achievement && (
            <View style={styles.achievementInfo}>
              <Award size={16} color="#FF9500" />
              <Text style={styles.achievementText}>{post.achievement}</Text>
              <Text style={styles.pointsText}>+{post.points} points</Text>
            </View>
          )}

          <View style={styles.postActions}>
            <TouchableOpacity style={styles.actionButton}>
              <Heart size={20} color="#FF3B30" />
              <Text style={styles.actionText}>{post.likes}</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <MessageCircle size={20} color="#007AFF" />
              <Text style={styles.actionText}>{post.comments}</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <Share2 size={20} color="#34C759" />
              <Text style={styles.actionText}>{post.shares}</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      ))}
    </View>
  );

  const renderChallenges = () => (
    <View style={styles.challengesSection}>
      {activeChallenges.map((challenge, index) => (
        <Animated.View
          key={challenge.title}
          entering={FadeInDown.delay(index * 150)}
          style={styles.challengeCard}>
          <Image source={{ uri: challenge.image }} style={styles.challengeImage} />
          <View style={styles.challengeContent}>
            <View style={styles.challengeHeader}>
              <Text style={styles.challengeTitle}>{challenge.title}</Text>
              <View style={[styles.categoryBadge, { backgroundColor: challenge.category === 'Fitness' ? '#007AFF' : challenge.category === 'Eco-Fitness' ? '#34C759' : '#FF9500' }]}>
                <Text style={styles.categoryText}>{challenge.category}</Text>
              </View>
            </View>
            
            <Text style={styles.challengeDescription}>{challenge.description}</Text>
            
            <View style={styles.challengeStats}>
              <View style={styles.challengeStat}>
                <Users size={16} color="#8E8E93" />
                <Text style={styles.challengeStatText}>{challenge.participants} joined</Text>
              </View>
              <View style={styles.challengeStat}>
                <Calendar size={16} color="#8E8E93" />
                <Text style={styles.challengeStatText}>{challenge.daysLeft} days left</Text>
              </View>
              <View style={styles.challengeStat}>
                <Trophy size={16} color="#FFD700" />
                <Text style={styles.challengeStatText}>{challenge.reward}</Text>
              </View>
            </View>

            <View style={styles.progressContainer}>
              <Text style={styles.progressLabel}>Progress: {challenge.progress}%</Text>
              <View style={styles.progressBar}>
                <View style={[styles.progressFill, { width: `${challenge.progress}%` }]} />
              </View>
            </View>

            <TouchableOpacity style={styles.joinButton}>
              <Text style={styles.joinButtonText}>Join Challenge</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      ))}
    </View>
  );

  const renderLeaderboard = () => (
    <View style={styles.leaderboardSection}>
      {leaderboard.map((user, index) => (
        <Animated.View
          key={user.rank}
          entering={FadeInRight.delay(index * 100)}
          style={[styles.leaderboardItem, index < 3 && styles.topThree]}>
          <View style={styles.rankContainer}>
            <Text style={styles.rankBadge}>{user.badge}</Text>
            <Text style={styles.rankNumber}>{user.rank}</Text>
          </View>
          <Image source={{ uri: user.avatar }} style={styles.leaderboardAvatar} />
          <View style={styles.leaderboardInfo}>
            <Text style={styles.leaderboardName}>{user.name}</Text>
            <Text style={styles.leaderboardPoints}>{user.points.toLocaleString()} points</Text>
          </View>
        </Animated.View>
      ))}
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#667eea" />
      
      {/* Simple Background */}
      <View style={styles.backgroundContainer}>
        <LinearGradient
          colors={['#667eea', '#764ba2', '#f093fb', '#f5576c']}
          style={styles.backgroundGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <LinearGradient
          colors={['rgba(102, 126, 234, 0.95)', 'rgba(118, 75, 162, 0.95)']}
          style={styles.header}>
          <View style={styles.headerContent}>
            <View>
              <Text style={styles.headerTitle}>Community</Text>
              <Text style={styles.headerSubtitle}>Connect • Challenge • Celebrate</Text>
            </View>
            <TouchableOpacity style={styles.notificationButton}>
              <MessageCircle size={24} color="#FFFFFF" />
            </TouchableOpacity>
          </View>

          <View style={styles.statsContainer}>
            {communityStats.map((stat, index) => (
              <Animated.View
                key={stat.label}
                entering={FadeInDown.delay(index * 100)}
                style={styles.statCard}>
                <View style={[styles.statIcon, { backgroundColor: stat.color }]}>
                  <stat.icon size={20} color="#FFFFFF" />
                </View>
                <Text style={styles.statValue}>{stat.value}</Text>
                <Text style={styles.statLabel}>{stat.label}</Text>
              </Animated.View>
            ))}
          </View>
        </LinearGradient>

        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'feed' && styles.activeTab]}
            onPress={() => setActiveTab('feed')}>
            <Text style={[styles.tabText, activeTab === 'feed' && styles.activeTabText]}>Feed</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'challenges' && styles.activeTab]}
            onPress={() => setActiveTab('challenges')}>
            <Text style={[styles.tabText, activeTab === 'challenges' && styles.activeTabText]}>Challenges</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'leaderboard' && styles.activeTab]}
            onPress={() => setActiveTab('leaderboard')}>
            <Text style={[styles.tabText, activeTab === 'leaderboard' && styles.activeTabText]}>Leaderboard</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.content}>
          {activeTab === 'feed' && renderFeed()}
          {activeTab === 'challenges' && renderChallenges()}
          {activeTab === 'leaderboard' && renderLeaderboard()}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
  },
  backgroundGradient: {
    flex: 1,
  },
  floatingElement: {
    position: 'absolute',
    borderRadius: 100,
    opacity: 0.1,
  },
  element1: {
    width: 200,
    height: 200,
    backgroundColor: '#FF6B35',
    top: '10%',
    left: '-10%',
  },
  element2: {
    width: 150,
    height: 150,
    backgroundColor: '#4ECDC4',
    top: '30%',
    right: '-5%',
  },
  element3: {
    width: 120,
    height: 120,
    backgroundColor: '#45B7D1',
    bottom: '20%',
    left: '10%',
  },
  element4: {
    width: 180,
    height: 180,
    backgroundColor: '#96CEB4',
    bottom: '5%',
    right: '-8%',
  },
  geometricPattern: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  geometricShape: {
    position: 'absolute',
    width: 20,
    height: 20,
    backgroundColor: '#FFFFFF',
    borderRadius: 4,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 60,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  headerTitle: {
    fontSize: 34,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  headerSubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.9,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  notificationButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 10,
  },
  statCard: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
  },
  statIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  statValue: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#000000',
    marginBottom: 2,
  },
  statLabel: {
    fontSize: 10,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
    textAlign: 'center',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    margin: 20,
    borderRadius: 12,
    padding: 4,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
  },
  activeTab: {
    backgroundColor: '#667eea',
  },
  tabText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#8E8E93',
  },
  activeTabText: {
    color: '#FFFFFF',
  },
  content: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  feedSection: {
    gap: 20,
  },
  createPostContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 16,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  createPostInput: {
    flex: 1,
    backgroundColor: '#F2F2F7',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  createPostPlaceholder: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
  },
  createPostButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#667eea',
    justifyContent: 'center',
    alignItems: 'center',
  },
  postCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 16,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  postHeader: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  postAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 12,
  },
  postUserInfo: {
    flex: 1,
  },
  postUserName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#000000',
    marginBottom: 2,
  },
  postMetaInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  postLocation: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
    marginLeft: 4,
  },
  postTime: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
    marginLeft: 4,
  },
  userBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  userLevel: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#FFD700',
  },
  postContent: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#000000',
    lineHeight: 24,
    marginBottom: 12,
  },
  postImage: {
    width: '100%',
    height: 200,
    borderRadius: 12,
    marginBottom: 12,
  },
  workoutInfo: {
    backgroundColor: '#F2F2F7',
    borderRadius: 12,
    padding: 12,
    marginBottom: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  workoutDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  workoutText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#007AFF',
  },
  caloriesText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
  },
  achievementInfo: {
    backgroundColor: '#FFF3E0',
    borderRadius: 12,
    padding: 12,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  achievementText: {
    flex: 1,
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#FF9500',
  },
  pointsText: {
    fontSize: 14,
    fontFamily: 'Inter-Bold',
    color: '#FF9500',
  },
  postActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#F2F2F7',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  actionText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#8E8E93',
  },
  challengesSection: {
    gap: 20,
  },
  challengeCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  challengeImage: {
    width: '100%',
    height: 160,
  },
  challengeContent: {
    padding: 16,
  },
  challengeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  challengeTitle: {
    flex: 1,
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#000000',
    marginRight: 10,
  },
  categoryBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  categoryText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  challengeDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
    marginBottom: 16,
  },
  challengeStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  challengeStat: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  challengeStatText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
  },
  progressContainer: {
    marginBottom: 16,
  },
  progressLabel: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#000000',
    marginBottom: 8,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#F2F2F7',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#34C759',
    borderRadius: 4,
  },
  joinButton: {
    backgroundColor: '#667eea',
    borderRadius: 12,
    paddingVertical: 14,
    alignItems: 'center',
  },
  joinButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  leaderboardSection: {
    gap: 12,
  },
  leaderboardItem: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 16,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  topThree: {
    borderWidth: 2,
    borderColor: '#FFD700',
  },
  rankContainer: {
    alignItems: 'center',
    marginRight: 16,
    minWidth: 40,
  },
  rankBadge: {
    fontSize: 24,
    marginBottom: 4,
  },
  rankNumber: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#8E8E93',
  },
  leaderboardAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 16,
  },
  leaderboardInfo: {
    flex: 1,
  },
  leaderboardName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#000000',
    marginBottom: 2,
  },
  leaderboardPoints: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
  },
});