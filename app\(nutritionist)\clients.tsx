import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, StatusBar, Image } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Search, Filter, Plus, Star, TrendingUp, Calendar, MessageCircle, Target, Apple, Scale } from 'lucide-react-native';
import Animated, { FadeInDown, FadeInRight } from 'react-native-reanimated';

export default function NutritionistClientsScreen() {
  const clients = [
    {
      id: '1',
      name: '<PERSON>',
      avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=100',
      goal: 'Weight Loss',
      currentWeight: '68kg',
      targetWeight: '60kg',
      progress: 75,
      lastConsultation: '3 days ago',
      rating: 4.9,
      status: 'active',
      adherence: 85,
    },
    {
      id: '2',
      name: '<PERSON>',
      avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=100',
      goal: 'Muscle Gain',
      currentWeight: '75kg',
      targetWeight: '80kg',
      progress: 60,
      lastConsultation: '1 week ago',
      rating: 4.8,
      status: 'active',
      adherence: 92,
    },
    {
      id: '3',
      name: 'Lisa Chen',
      avatar: 'https://images.pexels.com/photos/1239288/pexels-photo-1239288.jpeg?auto=compress&cs=tinysrgb&w=100',
      goal: 'Healthy Eating',
      currentWeight: '58kg',
      targetWeight: '58kg',
      progress: 90,
      lastConsultation: '2 days ago',
      rating: 5.0,
      status: 'active',
      adherence: 78,
    },
  ];

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#34C759" />
      
      <LinearGradient
        colors={['rgba(52, 199, 89, 0.95)', 'rgba(48, 209, 88, 0.95)']}
        style={styles.header}>
        <Text style={styles.headerTitle}>My Clients</Text>
        <Text style={styles.headerSubtitle}>Nutrition guidance & support</Text>
        
        <View style={styles.searchContainer}>
          <View style={styles.searchBar}>
            <Search size={20} color="#8E8E93" />
            <Text style={styles.searchPlaceholder}>Search clients...</Text>
          </View>
          <TouchableOpacity style={styles.filterButton}>
            <Filter size={20} color="#34C759" />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.statsRow}>
          <Animated.View entering={FadeInDown.delay(100)} style={styles.statCard}>
            <Text style={styles.statNumber}>18</Text>
            <Text style={styles.statLabel}>Active Clients</Text>
          </Animated.View>
          <Animated.View entering={FadeInDown.delay(200)} style={styles.statCard}>
            <Text style={styles.statNumber}>4.9</Text>
            <Text style={styles.statLabel}>Avg Rating</Text>
          </Animated.View>
          <Animated.View entering={FadeInDown.delay(300)} style={styles.statCard}>
            <Text style={styles.statNumber}>94%</Text>
            <Text style={styles.statLabel}>Success Rate</Text>
          </Animated.View>
        </View>

        <View style={styles.clientsSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Active Clients</Text>
            <TouchableOpacity style={styles.addClientButton}>
              <Plus size={20} color="#FFFFFF" />
            </TouchableOpacity>
          </View>

          {clients.map((client, index) => (
            <Animated.View
              key={client.id}
              entering={FadeInRight.delay(400 + index * 100)}
              style={styles.clientCard}>
              <Image source={{ uri: client.avatar }} style={styles.clientAvatar} />
              
              <View style={styles.clientInfo}>
                <Text style={styles.clientName}>{client.name}</Text>
                <Text style={styles.clientGoal}>{client.goal}</Text>
                <Text style={styles.lastConsultation}>Last: {client.lastConsultation}</Text>
              </View>

              <View style={styles.clientMetrics}>
                <View style={styles.weightInfo}>
                  <Scale size={14} color="#8E8E93" />
                  <Text style={styles.weightText}>{client.currentWeight} → {client.targetWeight}</Text>
                </View>
                
                <View style={styles.progressContainer}>
                  <Text style={styles.progressLabel}>Goal Progress</Text>
                  <View style={styles.progressBar}>
                    <View style={[styles.progressFill, { width: `${client.progress}%` }]} />
                  </View>
                  <Text style={styles.progressText}>{client.progress}%</Text>
                </View>
                
                <View style={styles.adherenceContainer}>
                  <Apple size={14} color="#34C759" />
                  <Text style={styles.adherenceText}>{client.adherence}% adherence</Text>
                </View>
                
                <View style={styles.ratingContainer}>
                  <Star size={14} color="#FFD700" fill="#FFD700" />
                  <Text style={styles.ratingText}>{client.rating}</Text>
                </View>
              </View>

              <View style={styles.clientActions}>
                <TouchableOpacity style={styles.actionButton}>
                  <Target size={16} color="#34C759" />
                </TouchableOpacity>
                <TouchableOpacity style={styles.actionButton}>
                  <Calendar size={16} color="#007AFF" />
                </TouchableOpacity>
                <TouchableOpacity style={styles.actionButton}>
                  <MessageCircle size={16} color="#FF9500" />
                </TouchableOpacity>
              </View>
            </Animated.View>
          ))}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    padding: 20,
    paddingTop: 60,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  headerTitle: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.9,
    marginBottom: 20,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  searchBar: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 12,
    padding: 12,
    gap: 8,
  },
  searchPlaceholder: {
    color: '#8E8E93',
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  filterButton: {
    padding: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 12,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  statsRow: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 24,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  statNumber: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#34C759',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
  },
  clientsSection: {
    marginBottom: 40,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#000000',
  },
  addClientButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#34C759',
    justifyContent: 'center',
    alignItems: 'center',
  },
  clientCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  clientAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 16,
  },
  clientInfo: {
    flex: 1,
  },
  clientName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#000000',
    marginBottom: 4,
  },
  clientGoal: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#34C759',
    marginBottom: 2,
  },
  lastConsultation: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
  },
  clientMetrics: {
    alignItems: 'center',
    marginRight: 16,
    minWidth: 100,
  },
  weightInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: 8,
  },
  weightText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
  },
  progressContainer: {
    alignItems: 'center',
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 10,
    fontFamily: 'Inter-Regular',
    color: '#8E8E93',
    marginBottom: 4,
  },
  progressBar: {
    width: 80,
    height: 4,
    backgroundColor: '#F2F2F7',
    borderRadius: 2,
    marginBottom: 4,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#34C759',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 10,
    fontFamily: 'Inter-SemiBold',
    color: '#34C759',
  },
  adherenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: 4,
  },
  adherenceText: {
    fontSize: 10,
    fontFamily: 'Inter-Regular',
    color: '#34C759',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  ratingText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#000000',
  },
  clientActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F2F2F7',
    justifyContent: 'center',
    alignItems: 'center',
  },
});