{"expo": {"name": "BulaFitX", "slug": "bulafitx", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "bulafitx", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/images/icon.png", "resizeMode": "contain", "backgroundColor": "#667eea"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.bulafitx.app"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/icon.png", "backgroundColor": "#667eea"}, "package": "com.bulafitx.app"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-font"], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}}}}